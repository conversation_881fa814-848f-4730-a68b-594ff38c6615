#pragma once

#include <string>
#include <fstream>
#include <memory>
#include <mutex>
#include <queue>
#include <thread>
#include <atomic>
#include <chrono>
#include <functional>

enum class LogLevel {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARNING = 3,
    ERROR = 4,
    CRITICAL = 5
};

struct LogEntry {
    LogLevel level;
    std::string message;
    std::string category;
    std::chrono::system_clock::time_point timestamp;
    std::thread::id threadId;
    std::string file;
    int line;
    std::string function;
};

class LogSink {
public:
    virtual ~LogSink() = default;
    virtual void Write(const LogEntry& entry) = 0;
    virtual void Flush() = 0;
};

class ConsoleSink : public LogSink {
private:
    std::mutex mutex_;
    bool useColors_;

public:
    ConsoleSink(bool useColors = true);
    void Write(const LogEntry& entry) override;
    void Flush() override;

private:
    void SetConsoleColor(LogLevel level);
    void ResetConsoleColor();
};

class FileSink : public LogSink {
private:
    std::ofstream file_;
    std::string filePath_;
    std::mutex mutex_;
    size_t maxFileSize_;
    int maxFiles_;
    size_t currentSize_;

public:
    FileSink(const std::string& filePath, size_t maxFileSize = 10 * 1024 * 1024, int maxFiles = 5);
    ~FileSink();
    
    void Write(const LogEntry& entry) override;
    void Flush() override;

private:
    void RotateFile();
    std::string FormatEntry(const LogEntry& entry);
};

class Logger {
private:
    static std::unique_ptr<Logger> instance_;
    static std::mutex instanceMutex_;
    
    std::vector<std::unique_ptr<LogSink>> sinks_;
    std::queue<LogEntry> logQueue_;
    std::mutex queueMutex_;
    std::condition_variable queueCondition_;
    std::thread workerThread_;
    std::atomic<bool> shouldStop_;
    LogLevel minLevel_;
    std::mutex sinksMutex_;

public:
    static Logger& GetInstance();
    static void Initialize(LogLevel minLevel = LogLevel::INFO);
    static void Shutdown();

    ~Logger();

    // Configuração
    void SetMinLevel(LogLevel level) { minLevel_ = level; }
    LogLevel GetMinLevel() const { return minLevel_; }
    
    void AddSink(std::unique_ptr<LogSink> sink);
    void RemoveSinks();
    
    // Logging
    void Log(LogLevel level, const std::string& message, const std::string& category = "",
             const std::string& file = "", int line = 0, const std::string& function = "");
    
    template<typename... Args>
    void Log(LogLevel level, const std::string& format, Args&&... args) {
        if (level < minLevel_) return;
        
        char buffer[4096];
        snprintf(buffer, sizeof(buffer), format.c_str(), std::forward<Args>(args)...);
        Log(level, std::string(buffer));
    }
    
    // Convenience methods
    void Trace(const std::string& message, const std::string& category = "");
    void Debug(const std::string& message, const std::string& category = "");
    void Info(const std::string& message, const std::string& category = "");
    void Warning(const std::string& message, const std::string& category = "");
    void Error(const std::string& message, const std::string& category = "");
    void Critical(const std::string& message, const std::string& category = "");
    
    template<typename... Args>
    void Trace(const std::string& format, Args&&... args) {
        Log(LogLevel::TRACE, format, std::forward<Args>(args)...);
    }
    
    template<typename... Args>
    void Debug(const std::string& format, Args&&... args) {
        Log(LogLevel::DEBUG, format, std::forward<Args>(args)...);
    }
    
    template<typename... Args>
    void Info(const std::string& format, Args&&... args) {
        Log(LogLevel::INFO, format, std::forward<Args>(args)...);
    }
    
    template<typename... Args>
    void Warning(const std::string& format, Args&&... args) {
        Log(LogLevel::WARNING, format, std::forward<Args>(args)...);
    }
    
    template<typename... Args>
    void Error(const std::string& format, Args&&... args) {
        Log(LogLevel::ERROR, format, std::forward<Args>(args)...);
    }
    
    template<typename... Args>
    void Critical(const std::string& format, Args&&... args) {
        Log(LogLevel::CRITICAL, format, std::forward<Args>(args)...);
    }
    
    void Flush();

private:
    Logger();
    void WorkerThread();
    void ProcessLogEntry(const LogEntry& entry);
};

// Utility functions
std::string LogLevelToString(LogLevel level);
LogLevel StringToLogLevel(const std::string& str);
std::string FormatTimestamp(const std::chrono::system_clock::time_point& time);

// Macros for convenient logging with file/line info
#define LOG_TRACE(msg, ...) Logger::GetInstance().Log(LogLevel::TRACE, msg, "", __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#define LOG_DEBUG(msg, ...) Logger::GetInstance().Log(LogLevel::DEBUG, msg, "", __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#define LOG_INFO(msg, ...) Logger::GetInstance().Log(LogLevel::INFO, msg, "", __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#define LOG_WARNING(msg, ...) Logger::GetInstance().Log(LogLevel::WARNING, msg, "", __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#define LOG_ERROR(msg, ...) Logger::GetInstance().Log(LogLevel::ERROR, msg, "", __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#define LOG_CRITICAL(msg, ...) Logger::GetInstance().Log(LogLevel::CRITICAL, msg, "", __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)

// Category-specific macros
#define LOG_MEMORY(level, msg, ...) Logger::GetInstance().Log(level, msg, "MEMORY", __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#define LOG_PATTERN(level, msg, ...) Logger::GetInstance().Log(level, msg, "PATTERN", __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#define LOG_SKIN(level, msg, ...) Logger::GetInstance().Log(level, msg, "SKIN", __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#define LOG_STEALTH(level, msg, ...) Logger::GetInstance().Log(level, msg, "STEALTH", __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#define LOG_UI(level, msg, ...) Logger::GetInstance().Log(level, msg, "UI", __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)

// Performance logging
class PerformanceTimer {
private:
    std::chrono::high_resolution_clock::time_point start_;
    std::string name_;
    LogLevel level_;

public:
    PerformanceTimer(const std::string& name, LogLevel level = LogLevel::DEBUG);
    ~PerformanceTimer();
    
    void Stop();
    double GetElapsedMs() const;
};

#define PERF_TIMER(name) PerformanceTimer _perf_timer(name)
#define PERF_TIMER_LEVEL(name, level) PerformanceTimer _perf_timer(name, level)
