{"application": {"name": "CS2 Educational Skin Changer", "version": "1.0.0", "author": "Educational Project", "description": "Ferramenta educacional para demonstrar conceitos de modificação de memória"}, "target": {"processName": "cs2.exe", "updateInterval": 100, "maxRetries": 5, "retryDelay": 1000}, "ui": {"windowWidth": 800, "windowHeight": 600, "theme": "dark", "fontSize": 14, "showConsole": true, "enableLogging": true}, "stealth": {"enableProcessHiding": true, "enableWindowHiding": true, "enableMemoryObfuscation": true, "enableAPIHooking": true, "enableRandomDelays": true, "enableDebuggerDetection": true, "minDelayMs": 50, "maxDelayMs": 200, "processBlacklist": ["cheatengine.exe", "processhacker.exe", "x64dbg.exe", "ollydbg.exe", "ida.exe", "ida64.exe", "wireshark.exe", "fiddler.exe"]}, "skins": {"autoApply": false, "saveOnExit": true, "loadOnStart": true, "defaultQuality": 4, "defaultWear": 0.1, "enableStatTrak": false, "enableCustomNames": false}, "memory": {"scanChunkSize": 4096, "maxScanTime": 30000, "enableCaching": true, "cacheTimeout": 300000, "verifyPatterns": true}, "logging": {"level": "INFO", "enableFileLogging": true, "logFilePath": "logs/skinchanger.log", "maxLogFileSize": 10485760, "enableConsoleLogging": true, "timestampFormat": "%Y-%m-%d %H:%M:%S"}, "security": {"enableIntegrityChecks": true, "enableAntiTampering": true, "enableObfuscation": true, "checkInterval": 5000, "exitOnTamper": false, "alertOnSuspiciousActivity": true}, "patterns": {"autoUpdate": true, "fallbackToManual": true, "enableDynamicScanning": true, "maxPatternAge": 86400000, "verifyBeforeUse": true}, "weapons": {"categories": ["Pistol", "Rifle", "SMG", "Shotgun", "<PERSON><PERSON><PERSON>", "LMG", "Knife", "Glove"], "enableKnives": true, "enableGloves": true, "enableStickers": false, "enableCharms": false}, "advanced": {"enableExperimentalFeatures": false, "debugMode": false, "verboseLogging": false, "enableProfiling": false, "enableCrashReporting": false, "autoUpdate": false}}