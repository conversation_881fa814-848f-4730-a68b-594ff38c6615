{"version": "1.0.0", "lastUpdated": "2024-01-01", "description": "Exemplo de configuração de skins para fins educacionais", "weapons": {"7": {"name": "AK-47", "category": "Rifle", "skins": [{"paintKit": 44, "name": "Dragon Lore", "wearFloat": 0.05, "seed": 661, "quality": 5, "statTrak": -1, "customName": "", "enabled": false}, {"paintKit": 179, "name": "Fire Serpent", "wearFloat": 0.15, "seed": 420, "quality": 4, "statTrak": -1, "customName": "", "enabled": false}, {"paintKit": 524, "name": "Redline", "wearFloat": 0.1, "seed": 123, "quality": 4, "statTrak": 1337, "customName": "Educational AK", "enabled": false}]}, "9": {"name": "AWP", "category": "<PERSON><PERSON><PERSON>", "skins": [{"paintKit": 344, "name": "Dragon Lore", "wearFloat": 0.03, "seed": 786, "quality": 5, "statTrak": -1, "customName": "", "enabled": false}, {"paintKit": 365, "name": "Asiimov", "wearFloat": 0.18, "seed": 555, "quality": 4, "statTrak": 2500, "customName": "", "enabled": false}, {"paintKit": 10, "name": "Lightning Strike", "wearFloat": 0.01, "seed": 999, "quality": 4, "statTrak": -1, "customName": "Educational AWP", "enabled": false}]}, "16": {"name": "M4A4", "category": "Rifle", "skins": [{"paintKit": 309, "name": "Howl", "wearFloat": 0.07, "seed": 321, "quality": 5, "statTrak": -1, "customName": "", "enabled": false}, {"paintKit": 365, "name": "Asiimov", "wearFloat": 0.2, "seed": 777, "quality": 4, "statTrak": 1500, "customName": "", "enabled": false}]}, "60": {"name": "M4A1-S", "category": "Rifle", "skins": [{"paintKit": 361, "name": "<PERSON>", "wearFloat": 0.02, "seed": 456, "quality": 4, "statTrak": -1, "customName": "", "enabled": false}, {"paintKit": 619, "name": "Hyper Beast", "wearFloat": 0.12, "seed": 888, "quality": 4, "statTrak": 800, "customName": "", "enabled": false}]}, "2": {"name": "Glock-18", "category": "Pistol", "skins": [{"paintKit": 38, "name": "Fade", "wearFloat": 0.01, "seed": 111, "quality": 4, "statTrak": 5000, "customName": "", "enabled": false}, {"paintKit": 167, "name": "Water Elemental", "wearFloat": 0.08, "seed": 222, "quality": 4, "statTrak": -1, "customName": "", "enabled": false}]}, "61": {"name": "USP-S", "category": "Pistol", "skins": [{"paintKit": 277, "name": "Kill Confirmed", "wearFloat": 0.05, "seed": 333, "quality": 4, "statTrak": 3000, "customName": "", "enabled": false}, {"paintKit": 653, "name": "Neo-Noir", "wearFloat": 0.15, "seed": 444, "quality": 4, "statTrak": -1, "customName": "", "enabled": false}]}, "1": {"name": "Desert Eagle", "category": "Pistol", "skins": [{"paintKit": 37, "name": "Blaze", "wearFloat": 0.01, "seed": 555, "quality": 4, "statTrak": -1, "customName": "", "enabled": false}, {"paintKit": 351, "name": "Conspiracy", "wearFloat": 0.1, "seed": 666, "quality": 4, "statTrak": 1200, "customName": "", "enabled": false}]}, "500": {"name": "Bayonet", "category": "Knife", "skins": [{"paintKit": 38, "name": "Fade", "wearFloat": 0.01, "seed": 777, "quality": 3, "statTrak": -1, "customName": "", "enabled": false}, {"paintKit": 44, "name": "<PERSON>", "wearFloat": 0.02, "seed": 888, "quality": 3, "statTrak": -1, "customName": "", "enabled": false}]}, "507": {"name": "Karambit", "category": "Knife", "skins": [{"paintKit": 38, "name": "Fade", "wearFloat": 0.01, "seed": 999, "quality": 3, "statTrak": -1, "customName": "", "enabled": false}, {"paintKit": 59, "name": "Case Hardened", "wearFloat": 0.05, "seed": 387, "quality": 3, "statTrak": -1, "customName": "", "enabled": false}]}, "5027": {"name": "Bloodhound Gloves", "category": "Glove", "skins": [{"paintKit": 10006, "name": "Charred", "wearFloat": 0.15, "seed": 123, "quality": 4, "statTrak": -1, "customName": "", "enabled": false}, {"paintKit": 10007, "name": "Snakebite", "wearFloat": 0.2, "seed": 456, "quality": 4, "statTrak": -1, "customName": "", "enabled": false}]}}, "presets": {"default": {"name": "Configuração Padrão", "description": "Skins básicas para demonstração", "weapons": {}}, "expensive": {"name": "Skins Caras", "description": "Skins de alto valor para demonstração", "weapons": {"7": 0, "9": 0, "16": 0, "500": 0, "507": 0}}, "stattrak": {"name": "StatTrak Collection", "description": "Skins com StatTrak habilitado", "weapons": {"7": 2, "9": 1, "2": 0, "61": 0, "1": 1}}}, "notes": ["Este arquivo é apenas para fins educacionais", "Os valores de paintKit podem estar desatualizados", "Sempre verifique a compatibilidade antes de usar", "Use apenas em ambientes de teste controlados"]}