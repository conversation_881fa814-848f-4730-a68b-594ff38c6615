#pragma once

#include <windows.h>
#include <string>
#include <functional>
#include <memory>

// Forward declarations para ImGui
struct ImGuiContext;
struct ImDrawData;

class Interface {
private:
    HWND windowHandle = nullptr;
    WNDCLASSEX windowClass = {};
    bool isInitialized = false;
    bool shouldClose = false;
    
    // ImGui context
    ImGuiContext* imguiContext = nullptr;
    
    // Window properties
    int windowWidth = 800;
    int windowHeight = 600;
    std::string windowTitle;
    
    // Rendering
    bool vsyncEnabled = true;
    
    // Callbacks
    std::function<void()> renderCallback;

public:
    Interface();
    ~Interface();

    // Inicialização
    bool Initialize(const std::string& title);
    void Shutdown();
    bool IsInitialized() const { return isInitialized; }

    // Loop principal
    void Run(std::function<void()> callback);
    void RequestClose() { shouldClose = true; }
    bool ShouldClose() const { return shouldClose; }

    // Frame rendering
    void BeginFrame();
    void EndFrame();
    void Present();

    // Window management
    void SetWindowSize(int width, int height);
    void SetWindowTitle(const std::string& title);
    void SetVSync(bool enabled) { vsyncEnabled = enabled; }
    
    HWND GetWindowHandle() const { return windowHandle; }
    
    // UI Components
    void StatusIndicator(const std::string& label, const std::string& status, bool isGood);
    void Separator();
    void Text(const std::string& text);
    void TextColored(const std::string& text, float r, float g, float b);
    
    // Input components
    bool Button(const std::string& label);
    bool Checkbox(const std::string& label, bool& value);
    bool SliderFloat(const std::string& label, float& value, float min, float max);
    bool SliderInt(const std::string& label, int& value, int min, int max);
    bool InputText(const std::string& label, std::string& text);
    bool Combo(const std::string& label, int& currentItem, const std::vector<std::string>& items);
    
    // Layout
    void BeginGroup();
    void EndGroup();
    void SameLine();
    void Spacing();
    void Indent();
    void Unindent();
    
    // Windows
    bool BeginWindow(const std::string& name, bool* open = nullptr);
    void EndWindow();
    bool BeginChild(const std::string& name, float width = 0, float height = 0);
    void EndChild();
    
    // Tabs
    bool BeginTabBar(const std::string& name);
    void EndTabBar();
    bool BeginTabItem(const std::string& name);
    void EndTabItem();
    
    // Tables
    bool BeginTable(const std::string& name, int columns);
    void EndTable();
    void TableNextRow();
    void TableNextColumn();
    void TableSetupColumn(const std::string& label);
    void TableHeadersRow();
    
    // Styling
    void PushStyleColor(int colorId, float r, float g, float b, float a = 1.0f);
    void PopStyleColor(int count = 1);
    void PushStyleVar(int varId, float value);
    void PushStyleVar(int varId, float x, float y);
    void PopStyleVar(int count = 1);

private:
    // Window procedures
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    LRESULT HandleMessage(UINT uMsg, WPARAM wParam, LPARAM lParam);
    
    // Initialization helpers
    bool CreateWindow();
    bool InitializeImGui();
    bool InitializeDirectX();
    void SetupImGuiStyle();
    
    // Cleanup
    void CleanupImGui();
    void CleanupDirectX();
    void CleanupWindow();
    
    // Message handling
    void ProcessMessages();
    
    // DirectX members (will be implemented with actual DirectX headers)
    void* d3dDevice = nullptr;
    void* d3dDeviceContext = nullptr;
    void* swapChain = nullptr;
    void* renderTargetView = nullptr;
};

// Namespace com constantes de cores para a UI
namespace UIColors {
    constexpr float RED[4] = {1.0f, 0.0f, 0.0f, 1.0f};
    constexpr float GREEN[4] = {0.0f, 1.0f, 0.0f, 1.0f};
    constexpr float BLUE[4] = {0.0f, 0.0f, 1.0f, 1.0f};
    constexpr float YELLOW[4] = {1.0f, 1.0f, 0.0f, 1.0f};
    constexpr float ORANGE[4] = {1.0f, 0.5f, 0.0f, 1.0f};
    constexpr float PURPLE[4] = {0.5f, 0.0f, 1.0f, 1.0f};
    constexpr float WHITE[4] = {1.0f, 1.0f, 1.0f, 1.0f};
    constexpr float BLACK[4] = {0.0f, 0.0f, 0.0f, 1.0f};
    constexpr float GRAY[4] = {0.5f, 0.5f, 0.5f, 1.0f};
    constexpr float DARK_GRAY[4] = {0.2f, 0.2f, 0.2f, 1.0f};
    constexpr float LIGHT_GRAY[4] = {0.8f, 0.8f, 0.8f, 1.0f};
}

// Namespace com IDs de estilo do ImGui
namespace UIStyle {
    // Color IDs
    constexpr int COL_TEXT = 0;
    constexpr int COL_TEXT_DISABLED = 1;
    constexpr int COL_WINDOW_BG = 2;
    constexpr int COL_CHILD_BG = 3;
    constexpr int COL_POPUP_BG = 4;
    constexpr int COL_BORDER = 5;
    constexpr int COL_BORDER_SHADOW = 6;
    constexpr int COL_FRAME_BG = 7;
    constexpr int COL_FRAME_BG_HOVERED = 8;
    constexpr int COL_FRAME_BG_ACTIVE = 9;
    constexpr int COL_TITLE_BG = 10;
    constexpr int COL_TITLE_BG_ACTIVE = 11;
    constexpr int COL_TITLE_BG_COLLAPSED = 12;
    constexpr int COL_MENU_BAR_BG = 13;
    constexpr int COL_SCROLLBAR_BG = 14;
    constexpr int COL_SCROLLBAR_GRAB = 15;
    constexpr int COL_SCROLLBAR_GRAB_HOVERED = 16;
    constexpr int COL_SCROLLBAR_GRAB_ACTIVE = 17;
    constexpr int COL_CHECK_MARK = 18;
    constexpr int COL_SLIDER_GRAB = 19;
    constexpr int COL_SLIDER_GRAB_ACTIVE = 20;
    constexpr int COL_BUTTON = 21;
    constexpr int COL_BUTTON_HOVERED = 22;
    constexpr int COL_BUTTON_ACTIVE = 23;
    constexpr int COL_HEADER = 24;
    constexpr int COL_HEADER_HOVERED = 25;
    constexpr int COL_HEADER_ACTIVE = 26;
    
    // Style variable IDs
    constexpr int VAR_ALPHA = 0;
    constexpr int VAR_WINDOW_PADDING = 1;
    constexpr int VAR_WINDOW_ROUNDING = 2;
    constexpr int VAR_WINDOW_BORDER_SIZE = 3;
    constexpr int VAR_CHILD_ROUNDING = 4;
    constexpr int VAR_CHILD_BORDER_SIZE = 5;
    constexpr int VAR_POPUP_ROUNDING = 6;
    constexpr int VAR_POPUP_BORDER_SIZE = 7;
    constexpr int VAR_FRAME_PADDING = 8;
    constexpr int VAR_FRAME_ROUNDING = 9;
    constexpr int VAR_FRAME_BORDER_SIZE = 10;
    constexpr int VAR_ITEM_SPACING = 11;
    constexpr int VAR_ITEM_INNER_SPACING = 12;
    constexpr int VAR_INDENT_SPACING = 13;
    constexpr int VAR_SCROLLBAR_SIZE = 14;
    constexpr int VAR_SCROLLBAR_ROUNDING = 15;
    constexpr int VAR_GRAB_MIN_SIZE = 16;
    constexpr int VAR_GRAB_ROUNDING = 17;
    constexpr int VAR_TAB_ROUNDING = 18;
    constexpr int VAR_BUTTON_TEXT_ALIGN = 19;
    constexpr int VAR_SELECTABLE_TEXT_ALIGN = 20;
}
