#pragma once

#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>

// Estrutura para armazenar informações de hooks
struct HookInfo {
    void* originalFunction = nullptr;
    void* hookFunction = nullptr;
    void* trampolineFunction = nullptr;
    std::string functionName;
    bool isActive = false;
};

// Estrutura para configurações de stealth
struct StealthConfig {
    bool enableProcessHiding = true;
    bool enableWindowHiding = true;
    bool enableMemoryObfuscation = true;
    bool enableAPIHooking = true;
    bool enableRandomDelays = true;
    bool enableDebuggerDetection = true;
    
    int minDelayMs = 50;
    int maxDelayMs = 200;
    
    std::vector<std::string> processBlacklist = {
        "cheatengine.exe",
        "processhacker.exe",
        "x64dbg.exe",
        "ollydbg.exe",
        "ida.exe",
        "ida64.exe"
    };
};

class AntiDetection {
private:
    StealthConfig config;
    std::vector<HookInfo> activeHooks;
    bool isInitialized = false;
    
    // Handles para threads de monitoramento
    HANDLE monitoringThread = nullptr;
    HANDLE debuggerCheckThread = nullptr;
    bool shouldStopMonitoring = false;
    
    // Callbacks
    std::function<void(const std::string&)> logCallback;
    std::function<void(const std::string&)> alertCallback;

public:
    AntiDetection();
    ~AntiDetection();

    // Inicialização
    bool Initialize();
    void Shutdown();
    bool IsInitialized() const { return isInitialized; }

    // Configuração
    void SetConfig(const StealthConfig& newConfig) { config = newConfig; }
    const StealthConfig& GetConfig() const { return config; }
    
    // Callbacks
    void SetLogCallback(std::function<void(const std::string&)> callback) { logCallback = callback; }
    void SetAlertCallback(std::function<void(const std::string&)> callback) { alertCallback = callback; }

    // Proteções principais
    bool EnableProcessHiding();
    bool EnableWindowHiding();
    bool EnableMemoryObfuscation();
    bool EnableAPIHooking();
    
    // Detecção de ameaças
    bool IsDebuggerPresent();
    bool IsVirtualMachine();
    bool IsSandbox();
    bool IsBlacklistedProcessRunning();
    
    // Utilitários de stealth
    void RandomDelay();
    void ObfuscateMemoryRegion(void* address, size_t size);
    void RestoreMemoryRegion(void* address, size_t size);
    
    // Monitoramento
    void StartMonitoring();
    void StopMonitoring();
    
    // Status
    std::string GetStatusReport() const;
    bool IsProtectionActive() const;

private:
    // Implementações de proteção
    bool HideFromProcessList();
    bool HideWindowFromTaskbar();
    bool InstallAPIHooks();
    void RemoveAPIHooks();
    
    // Hooks específicos
    static NTSTATUS NTAPI HookedNtQuerySystemInformation(
        ULONG SystemInformationClass,
        PVOID SystemInformation,
        ULONG SystemInformationLength,
        PULONG ReturnLength
    );
    
    static BOOL WINAPI HookedEnumWindows(
        WNDENUMPROC lpEnumFunc,
        LPARAM lParam
    );
    
    static HWND WINAPI HookedFindWindow(
        LPCSTR lpClassName,
        LPCSTR lpWindowName
    );
    
    // Detecção avançada
    bool CheckForHardwareBreakpoints();
    bool CheckForSoftwareBreakpoints();
    bool CheckForVMWareArtifacts();
    bool CheckForVirtualBoxArtifacts();
    bool CheckForSandboxArtifacts();
    
    // Threads de monitoramento
    static DWORD WINAPI MonitoringThreadProc(LPVOID lpParameter);
    static DWORD WINAPI DebuggerCheckThreadProc(LPVOID lpParameter);
    
    // Utilitários internos
    void Log(const std::string& message) const;
    void Alert(const std::string& message) const;
    bool InstallHook(const std::string& moduleName, const std::string& functionName, 
                    void* hookFunction, HookInfo& hookInfo);
    bool RemoveHook(HookInfo& hookInfo);
    
    // Ofuscação
    std::vector<uint8_t> GenerateRandomBytes(size_t count);
    void XORMemory(void* data, size_t size, uint8_t key);
    
    // Verificações de integridade
    bool VerifyCodeIntegrity();
    bool CheckForPatches();
};

// Namespace com constantes e utilitários
namespace StealthUtils {
    // Constantes para detecção de VM
    constexpr const char* VMWARE_PROCESSES[] = {
        "vmtoolsd.exe",
        "vmwaretray.exe",
        "vmwareuser.exe"
    };
    
    constexpr const char* VIRTUALBOX_PROCESSES[] = {
        "vboxservice.exe",
        "vboxtray.exe"
    };
    
    constexpr const char* SANDBOX_PROCESSES[] = {
        "sandboxiedcomlaunch.exe",
        "sandboxierpcss.exe"
    };
    
    // Registry keys para detecção de VM
    constexpr const char* VMWARE_REGISTRY_KEYS[] = {
        "SOFTWARE\\VMware, Inc.\\VMware Tools",
        "HARDWARE\\DEVICEMAP\\Scsi\\Scsi Port 0\\Scsi Bus 0\\Target Id 0\\Logical Unit Id 0"
    };
    
    constexpr const char* VIRTUALBOX_REGISTRY_KEYS[] = {
        "SOFTWARE\\Oracle\\VirtualBox Guest Additions",
        "HARDWARE\\DEVICEMAP\\Scsi\\Scsi Port 0\\Scsi Bus 0\\Target Id 0\\Logical Unit Id 0"
    };
    
    // Funções utilitárias
    bool IsProcessRunning(const std::string& processName);
    bool RegistryKeyExists(HKEY hKey, const std::string& subKey);
    std::vector<std::string> GetRunningProcesses();
    bool IsFilePresent(const std::string& filePath);
    
    // Timing utilities
    void AccurateDelay(int milliseconds);
    uint64_t GetHighResolutionTime();
    
    // Memory utilities
    bool IsAddressValid(void* address);
    bool IsMemoryReadable(void* address, size_t size);
    bool IsMemoryWritable(void* address, size_t size);
    
    // String obfuscation
    std::string ObfuscateString(const std::string& input);
    std::string DeobfuscateString(const std::string& obfuscated);
    
    // Hardware detection
    std::string GetCPUBrand();
    std::string GetMotherboardSerial();
    std::vector<std::string> GetInstalledSoftware();
}
