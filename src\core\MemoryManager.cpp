#include "core/MemoryManager.h"
#include <iostream>
#include <algorithm>

MemoryManager::MemoryManager() {
    SetDebugPrivileges();
}

MemoryManager::~MemoryManager() {
    DetachFromProcess();
}

bool MemoryManager::AttachToProcess(const std::string& targetProcessName) {
    // Desconectar do processo anterior se existir
    DetachFromProcess();
    
    processName = targetProcessName;
    processId = FindProcessId(targetProcessName);
    
    if (processId == 0) {
        return false;
    }
    
    // Abrir handle do processo com permissões necessárias
    processHandle = OpenProcess(
        PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION | PROCESS_QUERY_INFORMATION,
        FALSE, processId
    );
    
    if (processHandle == INVALID_HANDLE_VALUE) {
        std::cerr << "[ERROR] Falha ao abrir processo. Erro: " << GetLastError() << std::endl;
        return false;
    }
    
    // Obter endereço base e tamanho do módulo principal
    baseAddress = GetModuleBaseAddress(processId, targetProcessName);
    moduleSize = GetModuleSize(processId, targetProcessName);
    
    if (baseAddress == 0 || moduleSize == 0) {
        std::cerr << "[ERROR] Falha ao obter informações do módulo" << std::endl;
        DetachFromProcess();
        return false;
    }
    
    std::cout << "[INFO] Conectado ao processo " << targetProcessName 
              << " (PID: " << processId << ")" << std::endl;
    std::cout << "[INFO] Base: 0x" << std::hex << baseAddress 
              << ", Tamanho: 0x" << moduleSize << std::dec << std::endl;
    
    return true;
}

bool MemoryManager::IsProcessValid() const {
    if (processHandle == INVALID_HANDLE_VALUE || processId == 0) {
        return false;
    }
    
    // Verificar se o processo ainda está rodando
    DWORD exitCode;
    if (!GetExitCodeProcess(processHandle, &exitCode)) {
        return false;
    }
    
    return exitCode == STILL_ACTIVE;
}

void MemoryManager::DetachFromProcess() {
    if (processHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(processHandle);
        processHandle = INVALID_HANDLE_VALUE;
    }
    
    processId = 0;
    baseAddress = 0;
    moduleSize = 0;
    processName.clear();
}

bool MemoryManager::ReadMemory(uintptr_t address, void* buffer, size_t size) const {
    if (!IsProcessValid() || !buffer || size == 0) {
        return false;
    }
    
    SIZE_T bytesRead;
    bool result = ReadProcessMemory(processHandle, reinterpret_cast<LPCVOID>(address), 
                                   buffer, size, &bytesRead);
    
    return result && bytesRead == size;
}

bool MemoryManager::WriteMemory(uintptr_t address, const void* buffer, size_t size) const {
    if (!IsProcessValid() || !buffer || size == 0) {
        return false;
    }
    
    SIZE_T bytesWritten;
    bool result = WriteProcessMemory(processHandle, reinterpret_cast<LPVOID>(address), 
                                    buffer, size, &bytesWritten);
    
    return result && bytesWritten == size;
}

bool MemoryManager::ProtectMemory(uintptr_t address, size_t size, DWORD newProtection, DWORD& oldProtection) const {
    if (!IsProcessValid()) {
        return false;
    }
    
    return VirtualProtectEx(processHandle, reinterpret_cast<LPVOID>(address), 
                           size, newProtection, &oldProtection) != 0;
}

bool MemoryManager::RestoreMemoryProtection(uintptr_t address, size_t size, DWORD protection) const {
    if (!IsProcessValid()) {
        return false;
    }
    
    DWORD dummy;
    return VirtualProtectEx(processHandle, reinterpret_cast<LPVOID>(address), 
                           size, protection, &dummy) != 0;
}

std::vector<uintptr_t> MemoryManager::FindPattern(const std::string& pattern, const std::string& mask) const {
    std::vector<uintptr_t> results;
    
    if (!IsProcessValid() || pattern.empty() || mask.empty() || pattern.size() != mask.size()) {
        return results;
    }
    
    const size_t patternSize = pattern.size();
    const size_t chunkSize = 4096; // Ler em chunks de 4KB
    
    for (uintptr_t addr = baseAddress; addr < baseAddress + moduleSize - patternSize; addr += chunkSize - patternSize + 1) {
        std::vector<uint8_t> buffer(chunkSize);
        size_t readSize = std::min(chunkSize, static_cast<size_t>((baseAddress + moduleSize) - addr));
        
        if (!ReadMemory(addr, buffer.data(), readSize)) {
            continue;
        }
        
        // Buscar padrão no buffer
        for (size_t i = 0; i <= readSize - patternSize; ++i) {
            bool found = true;
            for (size_t j = 0; j < patternSize; ++j) {
                if (mask[j] == 'x' && buffer[i + j] != static_cast<uint8_t>(pattern[j])) {
                    found = false;
                    break;
                }
            }
            
            if (found) {
                results.push_back(addr + i);
            }
        }
    }
    
    return results;
}

uintptr_t MemoryManager::FindPatternFirst(const std::string& pattern, const std::string& mask) const {
    auto results = FindPattern(pattern, mask);
    return results.empty() ? 0 : results[0];
}

bool MemoryManager::IsValidAddress(uintptr_t address) const {
    if (!IsProcessValid()) {
        return false;
    }
    
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQueryEx(processHandle, reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi)) == 0) {
        return false;
    }
    
    return (mbi.State == MEM_COMMIT) && 
           (mbi.Protect & (PAGE_READONLY | PAGE_READWRITE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE));
}

std::vector<MEMORY_BASIC_INFORMATION> MemoryManager::GetMemoryRegions() const {
    std::vector<MEMORY_BASIC_INFORMATION> regions;
    
    if (!IsProcessValid()) {
        return regions;
    }
    
    uintptr_t address = 0;
    MEMORY_BASIC_INFORMATION mbi;
    
    while (VirtualQueryEx(processHandle, reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi)) == sizeof(mbi)) {
        regions.push_back(mbi);
        address = reinterpret_cast<uintptr_t>(mbi.BaseAddress) + mbi.RegionSize;
    }
    
    return regions;
}

DWORD MemoryManager::FindProcessId(const std::string& targetProcessName) const {
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return 0;
    }
    
    PROCESSENTRY32 processEntry;
    processEntry.dwSize = sizeof(PROCESSENTRY32);
    
    if (Process32First(snapshot, &processEntry)) {
        do {
            if (targetProcessName == processEntry.szExeFile) {
                CloseHandle(snapshot);
                return processEntry.th32ProcessID;
            }
        } while (Process32Next(snapshot, &processEntry));
    }
    
    CloseHandle(snapshot);
    return 0;
}

uintptr_t MemoryManager::GetModuleBaseAddress(DWORD targetProcessId, const std::string& moduleName) const {
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, targetProcessId);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return 0;
    }
    
    MODULEENTRY32 moduleEntry;
    moduleEntry.dwSize = sizeof(MODULEENTRY32);
    
    if (Module32First(snapshot, &moduleEntry)) {
        do {
            if (moduleName == moduleEntry.szModule) {
                CloseHandle(snapshot);
                return reinterpret_cast<uintptr_t>(moduleEntry.modBaseAddr);
            }
        } while (Module32Next(snapshot, &moduleEntry));
    }
    
    CloseHandle(snapshot);
    return 0;
}

size_t MemoryManager::GetModuleSize(DWORD targetProcessId, const std::string& moduleName) const {
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, targetProcessId);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return 0;
    }
    
    MODULEENTRY32 moduleEntry;
    moduleEntry.dwSize = sizeof(MODULEENTRY32);
    
    if (Module32First(snapshot, &moduleEntry)) {
        do {
            if (moduleName == moduleEntry.szModule) {
                CloseHandle(snapshot);
                return moduleEntry.modBaseSize;
            }
        } while (Module32Next(snapshot, &moduleEntry));
    }
    
    CloseHandle(snapshot);
    return 0;
}

bool MemoryManager::SetDebugPrivileges() {
    HANDLE token;
    if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &token)) {
        return false;
    }
    
    TOKEN_PRIVILEGES privileges;
    privileges.PrivilegeCount = 1;
    privileges.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;
    
    if (!LookupPrivilegeValue(nullptr, SE_DEBUG_NAME, &privileges.Privileges[0].Luid)) {
        CloseHandle(token);
        return false;
    }
    
    bool result = AdjustTokenPrivileges(token, FALSE, &privileges, sizeof(privileges), nullptr, nullptr) != 0;
    CloseHandle(token);
    
    return result;
}

// Implementação da classe MemoryProtectionGuard
MemoryProtectionGuard::MemoryProtectionGuard(const MemoryManager* manager, uintptr_t addr, size_t sz, DWORD newProtection)
    : memManager(manager), address(addr), size(sz), originalProtection(0), isActive(false) {
    
    if (memManager && memManager->ProtectMemory(address, size, newProtection, originalProtection)) {
        isActive = true;
    }
}

MemoryProtectionGuard::~MemoryProtectionGuard() {
    if (isActive && memManager) {
        memManager->RestoreMemoryProtection(address, size, originalProtection);
    }
}

MemoryProtectionGuard::MemoryProtectionGuard(MemoryProtectionGuard&& other) noexcept
    : memManager(other.memManager), address(other.address), size(other.size), 
      originalProtection(other.originalProtection), isActive(other.isActive) {
    other.isActive = false;
}

MemoryProtectionGuard& MemoryProtectionGuard::operator=(MemoryProtectionGuard&& other) noexcept {
    if (this != &other) {
        if (isActive && memManager) {
            memManager->RestoreMemoryProtection(address, size, originalProtection);
        }
        
        memManager = other.memManager;
        address = other.address;
        size = other.size;
        originalProtection = other.originalProtection;
        isActive = other.isActive;
        
        other.isActive = false;
    }
    return *this;
}
