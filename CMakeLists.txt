cmake_minimum_required(VERSION 3.20)
project(CS2SkinChanger)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Configurações para Release
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Flags de compilação
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG -ffast-math")
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")

# Incluir diretórios
include_directories(${CMAKE_SOURCE_DIR}/src)
include_directories(${CMAKE_SOURCE_DIR}/include)
include_directories(${CMAKE_SOURCE_DIR}/external)

# Encontrar pacotes necessários
find_package(OpenGL REQUIRED)

# Arquivos fonte
file(GLOB_RECURSE SOURCES 
    "src/*.cpp"
    "src/*.c"
)

file(GLOB_RECURSE HEADERS 
    "include/*.h"
    "include/*.hpp"
)

# Criar executável
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Linkar bibliotecas
target_link_libraries(${PROJECT_NAME} 
    ${OPENGL_LIBRARIES}
    user32
    kernel32
    gdi32
    winspool
    shell32
    ole32
    oleaut32
    uuid
    comdlg32
    advapi32
    psapi
    ntdll
)

# Propriedades do executável
set_target_properties(${PROJECT_NAME} PROPERTIES
    WIN32_EXECUTABLE TRUE
    OUTPUT_NAME "CS2SkinChanger"
)

# Copiar recursos para o diretório de build
file(COPY ${CMAKE_SOURCE_DIR}/resources DESTINATION ${CMAKE_BINARY_DIR})
