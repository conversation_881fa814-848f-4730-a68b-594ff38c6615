cmake_minimum_required(VERSION 3.20)
project(CS2SkinChanger VERSION 1.0.0 LANGUAGES CXX)

# Project configuration
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build type configuration
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific flags
if(MSVC)
    set(CMAKE_CXX_FLAGS_RELEASE "/O2 /DNDEBUG /GL /Gy")
    set(CMAKE_CXX_FLAGS_DEBUG "/Od /Zi /DDEBUG /D_DEBUG")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4 /permissive- /Zc:__cplusplus")
    # Enable static runtime for release builds
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
else()
    set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG -ffast-math -flto")
    set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG -fsanitize=address")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic")
endif()

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/include)
include_directories(${CMAKE_SOURCE_DIR}/external)

# Find required packages
find_package(OpenGL REQUIRED)

# External dependencies
include(FetchContent)

# nlohmann/json for configuration
FetchContent_Declare(
    nlohmann_json
    GIT_REPOSITORY https://github.com/nlohmann/json.git
    GIT_TAG v3.11.2
)
FetchContent_MakeAvailable(nlohmann_json)

# ImGui for UI (if not using placeholder)
option(USE_REAL_IMGUI "Use real ImGui implementation" OFF)
if(USE_REAL_IMGUI)
    FetchContent_Declare(
        imgui
        GIT_REPOSITORY https://github.com/ocornut/imgui.git
        GIT_TAG v1.89.9
    )
    FetchContent_MakeAvailable(imgui)
endif()

# Source files organization
file(GLOB_RECURSE CORE_SOURCES "src/core/*.cpp")
file(GLOB_RECURSE SKIN_SOURCES "src/skin/*.cpp")
file(GLOB_RECURSE UI_SOURCES "src/ui/*.cpp")
file(GLOB_RECURSE STEALTH_SOURCES "src/stealth/*.cpp")
file(GLOB_RECURSE UTILS_SOURCES "src/utils/*.cpp")

set(MAIN_SOURCES "src/main.cpp")

set(ALL_SOURCES
    ${MAIN_SOURCES}
    ${CORE_SOURCES}
    ${SKIN_SOURCES}
    ${UI_SOURCES}
    ${STEALTH_SOURCES}
    ${UTILS_SOURCES}
)

file(GLOB_RECURSE HEADERS
    "include/*.h"
    "include/*.hpp"
)

# Create main executable
add_executable(${PROJECT_NAME} ${ALL_SOURCES} ${HEADERS})

# Target properties
set_target_properties(${PROJECT_NAME} PROPERTIES
    WIN32_EXECUTABLE TRUE
    OUTPUT_NAME "CS2SkinChanger"
    VERSION ${PROJECT_VERSION}
    DESCRIPTION "CS2 Educational Skin Changer"
)

# Link libraries
target_link_libraries(${PROJECT_NAME}
    PRIVATE
    nlohmann_json::nlohmann_json
    ${OPENGL_LIBRARIES}
    user32
    kernel32
    gdi32
    winspool
    shell32
    ole32
    oleaut32
    uuid
    comdlg32
    advapi32
    psapi
    ntdll
)

# Compiler definitions
target_compile_definitions(${PROJECT_NAME} PRIVATE
    $<$<CONFIG:Debug>:DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
    NOMINMAX
    WIN32_LEAN_AND_MEAN
    _CRT_SECURE_NO_WARNINGS
)

# Include directories for target
target_include_directories(${PROJECT_NAME} PRIVATE
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/external
)

# Testing configuration
option(BUILD_TESTS "Build test suite" ON)
if(BUILD_TESTS)
    enable_testing()

    # Test framework sources
    file(GLOB_RECURSE TEST_FRAMEWORK_SOURCES "tests/TestFramework.cpp")
    file(GLOB_RECURSE TEST_SOURCES "tests/*Tests.cpp")

    # Create test executable
    add_executable(${PROJECT_NAME}_Tests
        ${TEST_FRAMEWORK_SOURCES}
        ${TEST_SOURCES}
        ${CORE_SOURCES}
        ${SKIN_SOURCES}
        ${STEALTH_SOURCES}
        ${UTILS_SOURCES}
    )

    target_link_libraries(${PROJECT_NAME}_Tests
        PRIVATE
        nlohmann_json::nlohmann_json
        ${OPENGL_LIBRARIES}
        user32
        kernel32
        gdi32
        advapi32
        psapi
        ntdll
    )

    target_include_directories(${PROJECT_NAME}_Tests PRIVATE
        ${CMAKE_SOURCE_DIR}/include
        ${CMAKE_SOURCE_DIR}/tests
    )

    target_compile_definitions(${PROJECT_NAME}_Tests PRIVATE
        $<$<CONFIG:Debug>:DEBUG>
        $<$<CONFIG:Release>:NDEBUG>
        NOMINMAX
        WIN32_LEAN_AND_MEAN
        _CRT_SECURE_NO_WARNINGS
        TESTING_BUILD
    )

    # Add tests
    add_test(NAME MemoryManagerTests COMMAND ${PROJECT_NAME}_Tests --suite "MemoryManager Tests")
    add_test(NAME PatternScannerTests COMMAND ${PROJECT_NAME}_Tests --suite "PatternScanner Tests")
    add_test(NAME SkinEngineTests COMMAND ${PROJECT_NAME}_Tests --suite "SkinEngine Tests")
    add_test(NAME ConfigTests COMMAND ${PROJECT_NAME}_Tests --suite "Config Tests")
endif()

# Resource copying
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_SOURCE_DIR}/resources
    $<TARGET_FILE_DIR:${PROJECT_NAME}>/resources
    COMMENT "Copying resources to build directory"
)

# Create logs directory
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory
    $<TARGET_FILE_DIR:${PROJECT_NAME}>/logs
    COMMENT "Creating logs directory"
)

# Installation configuration
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    COMPONENT Runtime
)

install(DIRECTORY ${CMAKE_SOURCE_DIR}/resources/
    DESTINATION bin/resources
    COMPONENT Runtime
)

install(FILES
    ${CMAKE_SOURCE_DIR}/README.md
    ${CMAKE_SOURCE_DIR}/USAGE_GUIDE.md
    DESTINATION .
    COMPONENT Documentation
)

# CPack configuration for packaging
set(CPACK_PACKAGE_NAME "CS2SkinChanger")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "CS2 Educational Skin Changer")
set(CPACK_PACKAGE_VENDOR "Educational Project")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

set(CPACK_RESOURCE_FILE_LICENSE "${CMAKE_SOURCE_DIR}/LICENSE")
set(CPACK_RESOURCE_FILE_README "${CMAKE_SOURCE_DIR}/README.md")

set(CPACK_GENERATOR "ZIP;NSIS")
set(CPACK_SOURCE_GENERATOR "ZIP")

include(CPack)

# Development tools
option(ENABLE_CLANG_TIDY "Enable clang-tidy analysis" OFF)
if(ENABLE_CLANG_TIDY)
    find_program(CLANG_TIDY_EXE NAMES "clang-tidy")
    if(CLANG_TIDY_EXE)
        set_target_properties(${PROJECT_NAME} PROPERTIES
            CXX_CLANG_TIDY "${CLANG_TIDY_EXE};-checks=-*,readability-*,performance-*,modernize-*"
        )
    endif()
endif()

# Print configuration summary
message(STATUS "=== CS2SkinChanger Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Build tests: ${BUILD_TESTS}")
message(STATUS "Use real ImGui: ${USE_REAL_IMGUI}")
message(STATUS "Enable clang-tidy: ${ENABLE_CLANG_TIDY}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "======================================")
