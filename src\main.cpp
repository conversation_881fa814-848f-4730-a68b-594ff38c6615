#include <windows.h>
#include <iostream>
#include <thread>
#include <chrono>
#include <exception>

#include "core/MemoryManager.h"
#include "core/PatternScanner.h"
#include "skin/SkinEngine.h"
#include "ui/Interface.h"
#include "stealth/AntiDetection.h"
#include "utils/Logger.h"
#include "utils/ConfigManager.h"
#include "utils/ErrorHandler.h"

// Configurações globais
namespace Config {
    constexpr const char* WINDOW_TITLE = "CS2 Educational Skin Changer";
    constexpr const char* TARGET_PROCESS = "cs2.exe";
    constexpr int UPDATE_INTERVAL_MS = 100;
}

class CS2SkinChanger {
private:
    std::unique_ptr<MemoryManager> memoryManager;
    std::unique_ptr<PatternScanner> patternScanner;
    std::unique_ptr<SkinEngine> skinEngine;
    std::unique_ptr<Interface> ui;
    std::unique_ptr<AntiDetection> antiDetection;

    bool isRunning = false;
    std::thread updateThread;

    // Performance monitoring
    std::chrono::steady_clock::time_point lastUpdate;
    double averageUpdateTime = 0.0;
    int updateCount = 0;

public:
    CS2SkinChanger() {
        LOG_INFO("Initializing CS2SkinChanger components...");

        try {
            // Inicializar componentes com tratamento de erro
            memoryManager = std::make_unique<MemoryManager>();
            patternScanner = std::make_unique<PatternScanner>();
            skinEngine = std::make_unique<SkinEngine>();
            ui = std::make_unique<Interface>();
            antiDetection = std::make_unique<AntiDetection>();

            lastUpdate = std::chrono::steady_clock::now();
            LOG_INFO("All components initialized successfully");
        } catch (const std::exception& e) {
            LOG_CRITICAL("Failed to initialize components: %s", e.what());
            throw;
        }
    }

    ~CS2SkinChanger() {
        Stop();
    }

    bool Initialize() {
        PERF_TIMER("CS2SkinChanger::Initialize");
        LOG_INFO("Initializing CS2 Skin Changer...");

        try {
            // Aplicar proteções anti-detecção
            if (!antiDetection->Initialize()) {
                LOG_ERROR("Failed to initialize anti-detection protections");
                return false;
            }
            LOG_INFO("Anti-detection system initialized");

            // Inicializar interface
            std::string windowTitle = CONFIG_GET("application", "name", std::string);
            if (!ui->Initialize(windowTitle)) {
                LOG_ERROR("Failed to initialize user interface");
                return false;
            }
            LOG_INFO("User interface initialized");

            // Tentar conectar ao processo CS2
            std::string targetProcess = CONFIG_GET("target", "processName", std::string);
            if (!memoryManager->AttachToProcess(targetProcess)) {
                LOG_WARNING("CS2 process not found. Waiting for process...");
            } else {
                LOG_INFO("Successfully attached to CS2 process");
            }

            LOG_INFO("Initialization completed successfully!");
            return true;
        } catch (const std::exception& e) {
            LOG_CRITICAL("Initialization failed with exception: %s", e.what());
            ErrorHandler::GetInstance().ReportException(e, "CS2SkinChanger::Initialize");
            return false;
        }
    }

    void Run() {
        isRunning = true;
        
        // Thread principal de atualização
        updateThread = std::thread([this]() {
            while (isRunning) {
                Update();
                std::this_thread::sleep_for(std::chrono::milliseconds(Config::UPDATE_INTERVAL_MS));
            }
        });

        // Loop principal da interface
        ui->Run([this]() {
            RenderUI();
        });
    }

    void Stop() {
        isRunning = false;
        if (updateThread.joinable()) {
            updateThread.join();
        }
    }

private:
    void Update() {
        PERF_TIMER_LEVEL("CS2SkinChanger::Update", LogLevel::TRACE);

        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::microseconds>(now - lastUpdate).count() / 1000.0;

        // Update performance statistics
        updateCount++;
        averageUpdateTime = (averageUpdateTime * (updateCount - 1) + elapsed) / updateCount;
        lastUpdate = now;

        try {
            // Verificar se ainda está conectado ao processo
            if (!memoryManager->IsProcessValid()) {
                std::string targetProcess = CONFIG_GET("target", "processName", std::string);
                if (memoryManager->AttachToProcess(targetProcess)) {
                    LOG_INFO("Reconnected to CS2 process!");
                    InitializeGameData();
                } else {
                    // Log only every 100 updates to avoid spam
                    if (updateCount % 100 == 0) {
                        LOG_DEBUG("Still waiting for CS2 process...");
                    }
                }
                return;
            }

            // Atualizar engine de skins
            skinEngine->Update();

            // Log performance every 1000 updates
            if (updateCount % 1000 == 0) {
                LOG_DEBUG("Average update time: %.2fms", averageUpdateTime);
            }
        } catch (const std::exception& e) {
            LOG_ERROR("Update failed with exception: %s", e.what());
            ErrorHandler::GetInstance().ReportException(e, "CS2SkinChanger::Update");
        }
    }

    void InitializeGameData() {
        std::cout << "[INFO] Inicializando dados do jogo..." << std::endl;
        
        // Escanear padrões necessários
        if (!patternScanner->ScanPatterns(memoryManager.get())) {
            std::cerr << "[ERROR] Falha ao escanear padrões de memória" << std::endl;
            return;
        }

        // Inicializar engine de skins
        if (!skinEngine->Initialize(memoryManager.get(), patternScanner.get())) {
            std::cerr << "[ERROR] Falha ao inicializar engine de skins" << std::endl;
            return;
        }

        std::cout << "[INFO] Dados do jogo inicializados com sucesso!" << std::endl;
    }

    void RenderUI() {
        // Renderizar interface principal
        ui->BeginFrame();
        
        // Status da conexão
        bool connected = memoryManager->IsProcessValid();
        ui->StatusIndicator("CS2 Status", connected ? "Conectado" : "Desconectado", connected);
        
        if (connected) {
            // Interface de skins
            skinEngine->RenderUI();
        }
        
        ui->EndFrame();
    }
};

int main() {
    // Configurar console para UTF-8
    SetConsoleOutputCP(CP_UTF8);

    try {
        // Initialize core systems
        Logger::Initialize(LogLevel::INFO);
        ConfigManager::Initialize("resources/config.json");
        ErrorHandler::Initialize();

        // Setup configuration
        ApplicationConfig::Initialize();
        TargetConfig::Initialize();
        StealthConfig::Initialize();
        SkinConfig::Initialize();

        LOG_INFO("=== CS2 Educational Skin Changer ===");
        LOG_WARNING("NOTICE: This software is for educational purposes only!");
        LOG_WARNING("Use at your own risk.");
        LOG_INFO("=====================================");

        // Load configuration
        if (!ConfigManager::GetInstance().LoadFromFile()) {
            LOG_WARNING("Could not load configuration file, using defaults");
        }

        // Set log level from config
        std::string logLevel = CONFIG_GET("logging", "level", std::string);
        Logger::GetInstance().SetMinLevel(StringToLogLevel(logLevel));

        LOG_INFO("Starting application...");

        CS2SkinChanger app;

        if (!app.Initialize()) {
            LOG_CRITICAL("Application initialization failed");
            return -1;
        }

        LOG_INFO("Application initialized successfully, starting main loop...");
        app.Run();

        LOG_INFO("Application shutting down...");

    } catch (const CS2Exception& e) {
        LOG_CRITICAL("CS2 Exception: [%s] %s", e.GetCode().c_str(), e.GetMessage().c_str());
        ErrorHandler::GetInstance().ReportCS2Exception(e);
        return -1;
    } catch (const std::exception& e) {
        LOG_CRITICAL("Unhandled exception: %s", e.what());
        ErrorHandler::GetInstance().ReportException(e, "main");
        return -1;
    }

    // Cleanup
    try {
        Logger::GetInstance().Flush();
        ConfigManager::GetInstance().SaveToFile();

        ErrorHandler::Shutdown();
        ConfigManager::Shutdown();
        Logger::Shutdown();
    } catch (...) {
        // Ignore cleanup errors
    }

    return 0;
}
