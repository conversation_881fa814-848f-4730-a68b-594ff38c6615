#include <windows.h>
#include <iostream>
#include <thread>
#include <chrono>

#include "core/MemoryManager.h"
#include "core/PatternScanner.h"
#include "skin/SkinEngine.h"
#include "ui/Interface.h"
#include "stealth/AntiDetection.h"

// Configurações globais
namespace Config {
    constexpr const char* WINDOW_TITLE = "CS2 Educational Skin Changer";
    constexpr const char* TARGET_PROCESS = "cs2.exe";
    constexpr int UPDATE_INTERVAL_MS = 100;
}

class CS2SkinChanger {
private:
    std::unique_ptr<MemoryManager> memoryManager;
    std::unique_ptr<PatternScanner> patternScanner;
    std::unique_ptr<SkinEngine> skinEngine;
    std::unique_ptr<Interface> ui;
    std::unique_ptr<AntiDetection> antiDetection;
    
    bool isRunning = false;
    std::thread updateThread;

public:
    CS2SkinChanger() {
        // Inicializar componentes
        memoryManager = std::make_unique<MemoryManager>();
        patternScanner = std::make_unique<PatternScanner>();
        skinEngine = std::make_unique<SkinEngine>();
        ui = std::make_unique<Interface>();
        antiDetection = std::make_unique<AntiDetection>();
    }

    ~CS2SkinChanger() {
        Stop();
    }

    bool Initialize() {
        std::cout << "[INFO] Inicializando CS2 Skin Changer..." << std::endl;
        
        // Aplicar proteções anti-detecção
        if (!antiDetection->Initialize()) {
            std::cerr << "[ERROR] Falha ao inicializar proteções anti-detecção" << std::endl;
            return false;
        }

        // Inicializar interface
        if (!ui->Initialize(Config::WINDOW_TITLE)) {
            std::cerr << "[ERROR] Falha ao inicializar interface" << std::endl;
            return false;
        }

        // Tentar conectar ao processo CS2
        if (!memoryManager->AttachToProcess(Config::TARGET_PROCESS)) {
            std::cout << "[WARNING] CS2 não encontrado. Aguardando..." << std::endl;
        }

        std::cout << "[INFO] Inicialização concluída com sucesso!" << std::endl;
        return true;
    }

    void Run() {
        isRunning = true;
        
        // Thread principal de atualização
        updateThread = std::thread([this]() {
            while (isRunning) {
                Update();
                std::this_thread::sleep_for(std::chrono::milliseconds(Config::UPDATE_INTERVAL_MS));
            }
        });

        // Loop principal da interface
        ui->Run([this]() {
            RenderUI();
        });
    }

    void Stop() {
        isRunning = false;
        if (updateThread.joinable()) {
            updateThread.join();
        }
    }

private:
    void Update() {
        // Verificar se ainda está conectado ao processo
        if (!memoryManager->IsProcessValid()) {
            if (memoryManager->AttachToProcess(Config::TARGET_PROCESS)) {
                std::cout << "[INFO] Reconectado ao CS2!" << std::endl;
                InitializeGameData();
            }
            return;
        }

        // Atualizar engine de skins
        skinEngine->Update();
    }

    void InitializeGameData() {
        std::cout << "[INFO] Inicializando dados do jogo..." << std::endl;
        
        // Escanear padrões necessários
        if (!patternScanner->ScanPatterns(memoryManager.get())) {
            std::cerr << "[ERROR] Falha ao escanear padrões de memória" << std::endl;
            return;
        }

        // Inicializar engine de skins
        if (!skinEngine->Initialize(memoryManager.get(), patternScanner.get())) {
            std::cerr << "[ERROR] Falha ao inicializar engine de skins" << std::endl;
            return;
        }

        std::cout << "[INFO] Dados do jogo inicializados com sucesso!" << std::endl;
    }

    void RenderUI() {
        // Renderizar interface principal
        ui->BeginFrame();
        
        // Status da conexão
        bool connected = memoryManager->IsProcessValid();
        ui->StatusIndicator("CS2 Status", connected ? "Conectado" : "Desconectado", connected);
        
        if (connected) {
            // Interface de skins
            skinEngine->RenderUI();
        }
        
        ui->EndFrame();
    }
};

int main() {
    // Configurar console para UTF-8
    SetConsoleOutputCP(CP_UTF8);
    
    std::cout << "=== CS2 Educational Skin Changer ===" << std::endl;
    std::cout << "AVISO: Este software é apenas para fins educacionais!" << std::endl;
    std::cout << "Uso por sua própria conta e risco." << std::endl;
    std::cout << "=====================================" << std::endl;

    try {
        CS2SkinChanger app;
        
        if (!app.Initialize()) {
            std::cerr << "[ERROR] Falha na inicialização" << std::endl;
            return -1;
        }
        
        app.Run();
        
    } catch (const std::exception& e) {
        std::cerr << "[EXCEPTION] " << e.what() << std::endl;
        return -1;
    }

    return 0;
}
