#include "utils/Logger.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <filesystem>
#include <windows.h>

// Static members
std::unique_ptr<Logger> Logger::instance_ = nullptr;
std::mutex Logger::instanceMutex_;

// ConsoleSink Implementation
ConsoleSink::ConsoleSink(bool useColors) : useColors_(useColors) {
    // Enable ANSI color codes on Windows
    if (useColors_) {
        HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
        DWORD dwMode = 0;
        GetConsoleMode(hOut, &dwMode);
        dwMode |= ENABLE_VIRTUAL_TERMINAL_PROCESSING;
        SetConsoleMode(hOut, dwMode);
    }
}

void ConsoleSink::Write(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (useColors_) {
        SetConsoleColor(entry.level);
    }
    
    std::cout << FormatTimestamp(entry.timestamp) << " ";
    std::cout << "[" << LogLevelToString(entry.level) << "] ";
    
    if (!entry.category.empty()) {
        std::cout << "[" << entry.category << "] ";
    }
    
    std::cout << entry.message;
    
    if (!entry.file.empty()) {
        std::filesystem::path filePath(entry.file);
        std::cout << " (" << filePath.filename().string() << ":" << entry.line << ")";
    }
    
    std::cout << std::endl;
    
    if (useColors_) {
        ResetConsoleColor();
    }
}

void ConsoleSink::Flush() {
    std::cout.flush();
}

void ConsoleSink::SetConsoleColor(LogLevel level) {
    switch (level) {
        case LogLevel::TRACE:
            std::cout << "\033[37m"; // White
            break;
        case LogLevel::DEBUG:
            std::cout << "\033[36m"; // Cyan
            break;
        case LogLevel::INFO:
            std::cout << "\033[32m"; // Green
            break;
        case LogLevel::WARNING:
            std::cout << "\033[33m"; // Yellow
            break;
        case LogLevel::ERROR:
            std::cout << "\033[31m"; // Red
            break;
        case LogLevel::CRITICAL:
            std::cout << "\033[35m"; // Magenta
            break;
    }
}

void ConsoleSink::ResetConsoleColor() {
    std::cout << "\033[0m";
}

// FileSink Implementation
FileSink::FileSink(const std::string& filePath, size_t maxFileSize, int maxFiles)
    : filePath_(filePath), maxFileSize_(maxFileSize), maxFiles_(maxFiles), currentSize_(0) {
    
    // Create directory if it doesn't exist
    std::filesystem::path path(filePath);
    std::filesystem::create_directories(path.parent_path());
    
    file_.open(filePath_, std::ios::app);
    if (file_.is_open()) {
        file_.seekp(0, std::ios::end);
        currentSize_ = file_.tellp();
    }
}

FileSink::~FileSink() {
    if (file_.is_open()) {
        file_.close();
    }
}

void FileSink::Write(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!file_.is_open()) {
        return;
    }
    
    std::string formatted = FormatEntry(entry);
    file_ << formatted << std::endl;
    currentSize_ += formatted.length() + 1;
    
    if (currentSize_ > maxFileSize_) {
        RotateFile();
    }
}

void FileSink::Flush() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (file_.is_open()) {
        file_.flush();
    }
}

void FileSink::RotateFile() {
    file_.close();
    
    // Rotate existing files
    for (int i = maxFiles_ - 1; i > 0; --i) {
        std::string oldFile = filePath_ + "." + std::to_string(i);
        std::string newFile = filePath_ + "." + std::to_string(i + 1);
        
        if (std::filesystem::exists(oldFile)) {
            if (i == maxFiles_ - 1) {
                std::filesystem::remove(newFile);
            }
            std::filesystem::rename(oldFile, newFile);
        }
    }
    
    // Move current file to .1
    if (std::filesystem::exists(filePath_)) {
        std::filesystem::rename(filePath_, filePath_ + ".1");
    }
    
    // Create new file
    file_.open(filePath_, std::ios::out);
    currentSize_ = 0;
}

std::string FileSink::FormatEntry(const LogEntry& entry) {
    std::ostringstream oss;
    
    oss << FormatTimestamp(entry.timestamp) << " ";
    oss << "[" << LogLevelToString(entry.level) << "] ";
    
    if (!entry.category.empty()) {
        oss << "[" << entry.category << "] ";
    }
    
    oss << "[Thread:" << entry.threadId << "] ";
    oss << entry.message;
    
    if (!entry.file.empty()) {
        std::filesystem::path filePath(entry.file);
        oss << " (" << filePath.filename().string() << ":" << entry.line;
        if (!entry.function.empty()) {
            oss << " in " << entry.function;
        }
        oss << ")";
    }
    
    return oss.str();
}

// Logger Implementation
Logger& Logger::GetInstance() {
    std::lock_guard<std::mutex> lock(instanceMutex_);
    if (!instance_) {
        instance_ = std::unique_ptr<Logger>(new Logger());
    }
    return *instance_;
}

void Logger::Initialize(LogLevel minLevel) {
    auto& logger = GetInstance();
    logger.SetMinLevel(minLevel);
    
    // Add default sinks
    logger.AddSink(std::make_unique<ConsoleSink>(true));
    logger.AddSink(std::make_unique<FileSink>("logs/skinchanger.log"));
}

void Logger::Shutdown() {
    std::lock_guard<std::mutex> lock(instanceMutex_);
    if (instance_) {
        instance_->shouldStop_ = true;
        instance_->queueCondition_.notify_all();
        
        if (instance_->workerThread_.joinable()) {
            instance_->workerThread_.join();
        }
        
        instance_.reset();
    }
}

Logger::Logger() : shouldStop_(false), minLevel_(LogLevel::INFO) {
    workerThread_ = std::thread(&Logger::WorkerThread, this);
}

Logger::~Logger() {
    shouldStop_ = true;
    queueCondition_.notify_all();
    
    if (workerThread_.joinable()) {
        workerThread_.join();
    }
}

void Logger::AddSink(std::unique_ptr<LogSink> sink) {
    std::lock_guard<std::mutex> lock(sinksMutex_);
    sinks_.push_back(std::move(sink));
}

void Logger::RemoveSinks() {
    std::lock_guard<std::mutex> lock(sinksMutex_);
    sinks_.clear();
}

void Logger::Log(LogLevel level, const std::string& message, const std::string& category,
                const std::string& file, int line, const std::string& function) {
    if (level < minLevel_) {
        return;
    }
    
    LogEntry entry;
    entry.level = level;
    entry.message = message;
    entry.category = category;
    entry.timestamp = std::chrono::system_clock::now();
    entry.threadId = std::this_thread::get_id();
    entry.file = file;
    entry.line = line;
    entry.function = function;
    
    {
        std::lock_guard<std::mutex> lock(queueMutex_);
        logQueue_.push(entry);
    }
    
    queueCondition_.notify_one();
}

void Logger::Trace(const std::string& message, const std::string& category) {
    Log(LogLevel::TRACE, message, category);
}

void Logger::Debug(const std::string& message, const std::string& category) {
    Log(LogLevel::DEBUG, message, category);
}

void Logger::Info(const std::string& message, const std::string& category) {
    Log(LogLevel::INFO, message, category);
}

void Logger::Warning(const std::string& message, const std::string& category) {
    Log(LogLevel::WARNING, message, category);
}

void Logger::Error(const std::string& message, const std::string& category) {
    Log(LogLevel::ERROR, message, category);
}

void Logger::Critical(const std::string& message, const std::string& category) {
    Log(LogLevel::CRITICAL, message, category);
}

void Logger::Flush() {
    // Process remaining entries
    std::unique_lock<std::mutex> lock(queueMutex_);
    while (!logQueue_.empty()) {
        LogEntry entry = logQueue_.front();
        logQueue_.pop();
        lock.unlock();
        
        ProcessLogEntry(entry);
        
        lock.lock();
    }
    
    // Flush all sinks
    std::lock_guard<std::mutex> sinkLock(sinksMutex_);
    for (auto& sink : sinks_) {
        sink->Flush();
    }
}

void Logger::WorkerThread() {
    while (!shouldStop_) {
        std::unique_lock<std::mutex> lock(queueMutex_);
        queueCondition_.wait(lock, [this] { return !logQueue_.empty() || shouldStop_; });
        
        while (!logQueue_.empty()) {
            LogEntry entry = logQueue_.front();
            logQueue_.pop();
            lock.unlock();
            
            ProcessLogEntry(entry);
            
            lock.lock();
        }
    }
}

void Logger::ProcessLogEntry(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(sinksMutex_);
    for (auto& sink : sinks_) {
        try {
            sink->Write(entry);
        } catch (const std::exception& e) {
            // Avoid infinite recursion by writing directly to console
            std::cerr << "Logger error: " << e.what() << std::endl;
        }
    }
}

// Utility functions
std::string LogLevelToString(LogLevel level) {
    switch (level) {
        case LogLevel::TRACE: return "TRACE";
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO: return "INFO";
        case LogLevel::WARNING: return "WARN";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::CRITICAL: return "CRIT";
        default: return "UNKNOWN";
    }
}

LogLevel StringToLogLevel(const std::string& str) {
    if (str == "TRACE") return LogLevel::TRACE;
    if (str == "DEBUG") return LogLevel::DEBUG;
    if (str == "INFO") return LogLevel::INFO;
    if (str == "WARN" || str == "WARNING") return LogLevel::WARNING;
    if (str == "ERROR") return LogLevel::ERROR;
    if (str == "CRIT" || str == "CRITICAL") return LogLevel::CRITICAL;
    return LogLevel::INFO;
}

std::string FormatTimestamp(const std::chrono::system_clock::time_point& time) {
    auto time_t = std::chrono::system_clock::to_time_t(time);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        time.time_since_epoch()) % 1000;
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << ms.count();
    
    return oss.str();
}

// PerformanceTimer Implementation
PerformanceTimer::PerformanceTimer(const std::string& name, LogLevel level)
    : name_(name), level_(level) {
    start_ = std::chrono::high_resolution_clock::now();
}

PerformanceTimer::~PerformanceTimer() {
    Stop();
}

void PerformanceTimer::Stop() {
    if (start_.time_since_epoch().count() != 0) {
        double elapsed = GetElapsedMs();
        Logger::GetInstance().Log(level_, 
            "Performance: " + name_ + " took " + std::to_string(elapsed) + "ms", 
            "PERF");
        start_ = std::chrono::high_resolution_clock::time_point{};
    }
}

double PerformanceTimer::GetElapsedMs() const {
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start_);
    return duration.count() / 1000.0;
}
