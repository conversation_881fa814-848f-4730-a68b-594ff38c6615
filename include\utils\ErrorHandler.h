#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <exception>
#include <chrono>
#include <unordered_map>
#include <mutex>

enum class ErrorSeverity {
    INFO = 0,
    WARNING = 1,
    ERROR = 2,
    CRITICAL = 3,
    FATAL = 4
};

enum class ErrorCategory {
    GENERAL = 0,
    MEMORY = 1,
    PATTERN = 2,
    SKIN = 3,
    STEALTH = 4,
    UI = 5,
    CONFIG = 6,
    NETWORK = 7,
    FILE_IO = 8,
    VALIDATION = 9
};

struct ErrorInfo {
    std::string code;
    std::string message;
    std::string details;
    ErrorSeverity severity;
    ErrorCategory category;
    std::chrono::system_clock::time_point timestamp;
    std::string file;
    int line;
    std::string function;
    std::unordered_map<std::string, std::string> context;
    
    ErrorInfo(const std::string& code, const std::string& message, 
             ErrorSeverity severity = ErrorSeverity::ERROR,
             ErrorCategory category = ErrorCategory::GENERAL);
};

class CS2Exception : public std::exception {
private:
    ErrorInfo errorInfo_;

public:
    CS2Exception(const std::string& code, const std::string& message,
                ErrorSeverity severity = ErrorSeverity::ERROR,
                ErrorCategory category = ErrorCategory::GENERAL);
    
    CS2Exception(const ErrorInfo& errorInfo);
    
    const char* what() const noexcept override;
    const ErrorInfo& GetErrorInfo() const { return errorInfo_; }
    
    // Convenience methods
    const std::string& GetCode() const { return errorInfo_.code; }
    const std::string& GetMessage() const { return errorInfo_.message; }
    ErrorSeverity GetSeverity() const { return errorInfo_.severity; }
    ErrorCategory GetCategory() const { return errorInfo_.category; }
};

// Specialized exception types
class MemoryException : public CS2Exception {
public:
    MemoryException(const std::string& code, const std::string& message,
                   ErrorSeverity severity = ErrorSeverity::ERROR)
        : CS2Exception(code, message, severity, ErrorCategory::MEMORY) {}
};

class PatternException : public CS2Exception {
public:
    PatternException(const std::string& code, const std::string& message,
                    ErrorSeverity severity = ErrorSeverity::ERROR)
        : CS2Exception(code, message, severity, ErrorCategory::PATTERN) {}
};

class SkinException : public CS2Exception {
public:
    SkinException(const std::string& code, const std::string& message,
                 ErrorSeverity severity = ErrorSeverity::ERROR)
        : CS2Exception(code, message, severity, ErrorCategory::SKIN) {}
};

class StealthException : public CS2Exception {
public:
    StealthException(const std::string& code, const std::string& message,
                    ErrorSeverity severity = ErrorSeverity::ERROR)
        : CS2Exception(code, message, severity, ErrorCategory::STEALTH) {}
};

class ValidationException : public CS2Exception {
public:
    ValidationException(const std::string& code, const std::string& message,
                       ErrorSeverity severity = ErrorSeverity::ERROR)
        : CS2Exception(code, message, severity, ErrorCategory::VALIDATION) {}
};

class ErrorHandler {
private:
    static std::unique_ptr<ErrorHandler> instance_;
    static std::mutex instanceMutex_;
    
    std::vector<ErrorInfo> errorHistory_;
    std::mutex historyMutex_;
    size_t maxHistorySize_;
    
    std::vector<std::function<void(const ErrorInfo&)>> errorCallbacks_;
    std::mutex callbacksMutex_;
    
    std::unordered_map<std::string, int> errorCounts_;
    std::mutex countsMutex_;
    
    bool enableCrashDumps_;
    std::string crashDumpPath_;

public:
    static ErrorHandler& GetInstance();
    static void Initialize(size_t maxHistorySize = 1000);
    static void Shutdown();
    
    ~ErrorHandler() = default;
    
    // Error reporting
    void ReportError(const ErrorInfo& error);
    void ReportError(const std::string& code, const std::string& message,
                    ErrorSeverity severity = ErrorSeverity::ERROR,
                    ErrorCategory category = ErrorCategory::GENERAL,
                    const std::string& file = "", int line = 0,
                    const std::string& function = "");
    
    void ReportException(const std::exception& ex, const std::string& context = "");
    void ReportCS2Exception(const CS2Exception& ex);
    
    // Error history
    std::vector<ErrorInfo> GetErrorHistory() const;
    std::vector<ErrorInfo> GetErrorsByCategory(ErrorCategory category) const;
    std::vector<ErrorInfo> GetErrorsBySeverity(ErrorSeverity severity) const;
    std::vector<ErrorInfo> GetRecentErrors(std::chrono::minutes duration) const;
    
    void ClearHistory();
    size_t GetHistorySize() const;
    
    // Error statistics
    int GetErrorCount(const std::string& code) const;
    std::unordered_map<std::string, int> GetAllErrorCounts() const;
    void ResetErrorCounts();
    
    // Callbacks
    void AddErrorCallback(std::function<void(const ErrorInfo&)> callback);
    void RemoveAllCallbacks();
    
    // Configuration
    void SetMaxHistorySize(size_t size);
    size_t GetMaxHistorySize() const { return maxHistorySize_; }
    
    void SetCrashDumpsEnabled(bool enabled) { enableCrashDumps_ = enabled; }
    bool IsCrashDumpsEnabled() const { return enableCrashDumps_; }
    
    void SetCrashDumpPath(const std::string& path) { crashDumpPath_ = path; }
    const std::string& GetCrashDumpPath() const { return crashDumpPath_; }
    
    // Utility methods
    std::string FormatError(const ErrorInfo& error) const;
    void PrintErrorSummary() const;
    bool SaveErrorReport(const std::string& filePath) const;

private:
    ErrorHandler();
    void TrimHistory();
    void IncrementErrorCount(const std::string& code);
    void SetupCrashHandler();
};

// Result template for error handling without exceptions
template<typename T>
class Result {
private:
    bool success_;
    T value_;
    ErrorInfo error_;

public:
    // Success constructor
    Result(const T& value) : success_(true), value_(value), error_("", "") {}
    Result(T&& value) : success_(true), value_(std::move(value)), error_("", "") {}
    
    // Error constructor
    Result(const ErrorInfo& error) : success_(false), value_{}, error_(error) {}
    Result(const std::string& code, const std::string& message,
           ErrorSeverity severity = ErrorSeverity::ERROR,
           ErrorCategory category = ErrorCategory::GENERAL)
        : success_(false), value_{}, error_(code, message, severity, category) {}
    
    // Check methods
    bool IsSuccess() const { return success_; }
    bool IsError() const { return !success_; }
    
    // Value access
    const T& GetValue() const {
        if (!success_) {
            throw CS2Exception(error_);
        }
        return value_;
    }
    
    T& GetValue() {
        if (!success_) {
            throw CS2Exception(error_);
        }
        return value_;
    }
    
    // Error access
    const ErrorInfo& GetError() const { return error_; }
    
    // Convenience operators
    explicit operator bool() const { return success_; }
    const T& operator*() const { return GetValue(); }
    T& operator*() { return GetValue(); }
    const T* operator->() const { return &GetValue(); }
    T* operator->() { return &GetValue(); }
};

// Specialized Result for void operations
template<>
class Result<void> {
private:
    bool success_;
    ErrorInfo error_;

public:
    // Success constructor
    Result() : success_(true), error_("", "") {}
    
    // Error constructor
    Result(const ErrorInfo& error) : success_(false), error_(error) {}
    Result(const std::string& code, const std::string& message,
           ErrorSeverity severity = ErrorSeverity::ERROR,
           ErrorCategory category = ErrorCategory::GENERAL)
        : success_(false), error_(code, message, severity, category) {}
    
    // Check methods
    bool IsSuccess() const { return success_; }
    bool IsError() const { return !success_; }
    
    // Error access
    const ErrorInfo& GetError() const { return error_; }
    
    // Convenience operators
    explicit operator bool() const { return success_; }
};

// Validation framework
class Validator {
public:
    template<typename T>
    static Result<void> ValidateRange(const T& value, const T& min, const T& max, 
                                     const std::string& fieldName) {
        if (value < min || value > max) {
            return Result<void>("VALIDATION_RANGE_ERROR", 
                              fieldName + " must be between " + std::to_string(min) + 
                              " and " + std::to_string(max),
                              ErrorSeverity::WARNING, ErrorCategory::VALIDATION);
        }
        return Result<void>();
    }
    
    static Result<void> ValidateNotEmpty(const std::string& value, const std::string& fieldName);
    static Result<void> ValidateEmail(const std::string& email);
    static Result<void> ValidateFilePath(const std::string& path);
    static Result<void> ValidateProcessName(const std::string& processName);
    static Result<void> ValidateMemoryAddress(uintptr_t address);
    static Result<void> ValidatePaintKit(int paintKit);
    static Result<void> ValidateWearFloat(float wear);
    static Result<void> ValidateSeed(int seed);
    static Result<void> ValidateQuality(int quality);
};

// Utility macros for error handling
#define TRY_RESULT(result) \
    do { \
        auto _result = (result); \
        if (_result.IsError()) { \
            ErrorHandler::GetInstance().ReportError(_result.GetError()); \
            return _result; \
        } \
    } while(0)

#define RETURN_ERROR(code, message, severity, category) \
    do { \
        ErrorInfo _error(code, message, severity, category); \
        _error.file = __FILE__; \
        _error.line = __LINE__; \
        _error.function = __FUNCTION__; \
        ErrorHandler::GetInstance().ReportError(_error); \
        return Result<void>(_error); \
    } while(0)

#define RETURN_ERROR_T(type, code, message, severity, category) \
    do { \
        ErrorInfo _error(code, message, severity, category); \
        _error.file = __FILE__; \
        _error.line = __LINE__; \
        _error.function = __FUNCTION__; \
        ErrorHandler::GetInstance().ReportError(_error); \
        return Result<type>(_error); \
    } while(0)

#define VALIDATE_AND_RETURN(condition, code, message) \
    do { \
        if (!(condition)) { \
            RETURN_ERROR(code, message, ErrorSeverity::ERROR, ErrorCategory::VALIDATION); \
        } \
    } while(0)

// Error code constants
namespace ErrorCodes {
    // Memory errors
    constexpr const char* MEMORY_ACCESS_DENIED = "MEM_001";
    constexpr const char* MEMORY_INVALID_ADDRESS = "MEM_002";
    constexpr const char* MEMORY_READ_FAILED = "MEM_003";
    constexpr const char* MEMORY_WRITE_FAILED = "MEM_004";
    constexpr const char* MEMORY_PROTECTION_FAILED = "MEM_005";
    
    // Pattern errors
    constexpr const char* PATTERN_NOT_FOUND = "PAT_001";
    constexpr const char* PATTERN_INVALID = "PAT_002";
    constexpr const char* PATTERN_SCAN_TIMEOUT = "PAT_003";
    
    // Skin errors
    constexpr const char* SKIN_INVALID_PAINTKIT = "SKN_001";
    constexpr const char* SKIN_INVALID_WEAR = "SKN_002";
    constexpr const char* SKIN_INVALID_SEED = "SKN_003";
    constexpr const char* SKIN_APPLY_FAILED = "SKN_004";
    
    // Stealth errors
    constexpr const char* STEALTH_HOOK_FAILED = "STL_001";
    constexpr const char* STEALTH_DETECTION_FAILED = "STL_002";
    constexpr const char* STEALTH_DEBUGGER_DETECTED = "STL_003";
    
    // General errors
    constexpr const char* PROCESS_NOT_FOUND = "GEN_001";
    constexpr const char* INITIALIZATION_FAILED = "GEN_002";
    constexpr const char* CONFIGURATION_ERROR = "GEN_003";
    constexpr const char* FILE_NOT_FOUND = "GEN_004";
    constexpr const char* PERMISSION_DENIED = "GEN_005";
}
