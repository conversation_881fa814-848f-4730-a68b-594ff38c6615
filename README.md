# CS2 Educational Skin Changer

## ⚠️ AVISO IMPORTANTE

**Este projeto é EXCLUSIVAMENTE para fins educacionais e de pesquisa em segurança de software.**

- ❌ **NÃO use este software em servidores oficiais do CS2**
- ❌ **NÃO use este software para obter vantagens injustas**
- ❌ **O uso deste software pode resultar em banimento permanente (VAC Ban)**
- ✅ **Use apenas para aprender sobre engenharia reversa e segurança**
- ✅ **Use apenas em ambientes controlados e isolados**

## Descrição

Este projeto demonstra conceitos avançados de:
- Manipulação de memória de processos
- Pattern scanning e signature detection
- Técnicas anti-detecção e stealth
- Engenharia reversa de jogos
- Desenvolvimento de interfaces gráficas
- Arquitetura de software modular

## Características Técnicas

### Core Features
- **Memory Manager**: Sistema robusto para leitura/escrita de memória
- **Pattern Scanner**: Engine para localizar padrões de bytes na memória
- **Skin Engine**: Gerenciamento e aplicação de skins
- **Anti-Detection**: Técnicas para evitar detecção por sistemas anti-cheat
- **Modern UI**: Interface gráfica intuitiva usando ImGui

### Arquitetura
```
CS2SkinChanger/
├── src/
│   ├── core/           # Componentes principais
│   ├── skin/           # Engine de skins
│   ├── ui/             # Interface de usuário
│   └── stealth/        # Sistema anti-detecção
├── include/            # Headers
├── resources/          # Recursos e configurações
└── docs/              # Documentação
```

### Tecnologias Utilizadas
- **C++20**: Linguagem principal
- **Windows API**: Manipulação de processos e memória
- **ImGui**: Interface gráfica
- **DirectX 11**: Renderização
- **CMake**: Sistema de build
- **JSON**: Configuração

## Compilação

### Pré-requisitos
- Visual Studio 2022 ou superior
- Windows SDK 10.0.19041.0 ou superior
- CMake 3.20 ou superior
- Git

### Instruções
```bash
# Clonar o repositório
git clone <repository-url>
cd skin-changer-cs2

# Criar diretório de build
mkdir build
cd build

# Configurar com CMake
cmake .. -G "Visual Studio 17 2022" -A x64

# Compilar
cmake --build . --config Release
```

## Uso

### Configuração Inicial
1. Edite `resources/config.json` conforme necessário
2. Execute o programa como Administrador
3. Inicie o CS2 (apenas para testes em ambiente offline)
4. O programa detectará automaticamente o processo

### Interface
- **Status Panel**: Mostra o status da conexão
- **Weapon Selector**: Selecione a arma para modificar
- **Skin Browser**: Navegue pelas skins disponíveis
- **Properties**: Configure wear, seed, StatTrak, etc.
- **Stealth Panel**: Configure proteções anti-detecção

### Configurações de Stealth
```json
{
  "stealth": {
    "enableProcessHiding": true,
    "enableWindowHiding": true,
    "enableAPIHooking": true,
    "enableDebuggerDetection": true
  }
}
```

## Conceitos Educacionais Demonstrados

### 1. Memory Management
- Abertura de handles de processo
- Leitura/escrita de memória externa
- Proteção e desprotecção de memória
- Validação de endereços

### 2. Pattern Scanning
- Busca por assinaturas de bytes
- Máscaras de padrões
- Otimização de algoritmos de busca
- Cache de resultados

### 3. Anti-Detection Techniques
- Process hiding
- API hooking
- Debugger detection
- VM/Sandbox detection
- Memory obfuscation

### 4. Game Hacking Fundamentals
- Estruturas de dados de jogos
- Offsets e ponteiros
- Entity management
- Item modification

## Estrutura de Dados

### Skin Information
```cpp
struct SkinInfo {
    int paintKit;           // ID da skin
    float wearFloat;        // Desgaste (0.0-1.0)
    int seed;              // Seed do padrão
    int statTrak;          // Contador StatTrak
    std::string customName; // Nome personalizado
    int quality;           // Qualidade do item
};
```

### Memory Offsets
```cpp
struct MemoryOffsets {
    uintptr_t localPlayer;
    uintptr_t entityList;
    int paintKit;
    int itemDefinitionIndex;
    int wearFloat;
    // ... outros offsets
};
```

## Segurança e Ética

### Medidas de Segurança Implementadas
- Verificação de integridade do código
- Detecção de modificações
- Logs de auditoria
- Proteção contra engenharia reversa

### Considerações Éticas
- Este software não deve ser usado para trapacear
- Respeite os Termos de Serviço dos jogos
- Use apenas para aprendizado
- Não distribua para fins maliciosos

## Limitações Conhecidas

1. **Compatibilidade**: Funciona apenas no Windows
2. **Detecção**: Pode ser detectado por sistemas anti-cheat
3. **Atualizações**: Padrões podem ficar obsoletos
4. **Performance**: Impacto mínimo, mas mensurável

## Troubleshooting

### Problemas Comuns
- **Processo não encontrado**: Verifique se o CS2 está rodando
- **Acesso negado**: Execute como Administrador
- **Padrões não encontrados**: Atualize os padrões de memória
- **Interface não aparece**: Verifique drivers gráficos

### Logs
Verifique os logs em `logs/skinchanger.log` para diagnóstico.

## Contribuição

Este é um projeto educacional. Contribuições são bem-vindas para:
- Melhorias na documentação
- Otimizações de performance
- Novos recursos educacionais
- Correções de bugs

## Disclaimer Legal

**ESTE SOFTWARE É FORNECIDO "COMO ESTÁ", SEM GARANTIAS DE QUALQUER TIPO.**

O uso deste software é por sua própria conta e risco. Os desenvolvedores não se responsabilizam por:
- Banimentos de contas
- Perda de dados
- Problemas legais
- Danos ao sistema

## Licença

Este projeto é licenciado sob a MIT License - veja o arquivo LICENSE para detalhes.

## Recursos Educacionais

### Livros Recomendados
- "Reversing: Secrets of Reverse Engineering" - Eldad Eilam
- "The Shellcoder's Handbook" - Chris Anley
- "Windows Internals" - Mark Russinovich

### Ferramentas Úteis
- **Cheat Engine**: Para análise de memória
- **x64dbg**: Debugger avançado
- **Process Hacker**: Monitor de processos
- **HxD**: Editor hexadecimal

### Cursos Online
- Malware Analysis (Practical Malware Analysis)
- Reverse Engineering (OpenSecurityTraining)
- Windows Internals (Microsoft Learn)

---

**Lembre-se: O conhecimento é poder, mas com poder vem responsabilidade. Use este conhecimento de forma ética e responsável.**
