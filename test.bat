@echo off
echo ========================================
echo CS2 Educational Skin Changer - Test Script
echo ========================================
echo.

echo [INFO] Verificando estrutura do projeto...

REM Verificar arquivos principais
if not exist "CMakeLists.txt" (
    echo [ERROR] CMakeLists.txt não encontrado
    goto :error
)

if not exist "src\main.cpp" (
    echo [ERROR] main.cpp não encontrado
    goto :error
)

if not exist "include\core\MemoryManager.h" (
    echo [ERROR] MemoryManager.h não encontrado
    goto :error
)

if not exist "resources\config.json" (
    echo [ERROR] config.json não encontrado
    goto :error
)

echo [SUCCESS] Estrutura do projeto OK

echo.
echo [INFO] Verificando dependências...

REM Verificar CMake
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] CMake não encontrado
    goto :error
)
echo [SUCCESS] CMake encontrado

REM Verificar Visual Studio
where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Compilador não encontrado no PATH
    echo [INFO] Tentando configurar ambiente do Visual Studio...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    if %errorlevel% neq 0 (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
        if %errorlevel% neq 0 (
            echo [ERROR] Visual Studio não encontrado
            goto :error
        )
    )
)
echo [SUCCESS] Compilador encontrado

echo.
echo [INFO] Testando configuração do CMake...

if not exist build mkdir build
cd build

cmake .. -G "Visual Studio 17 2022" -A x64 >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Falha na configuração do CMake
    cd ..
    goto :error
)

echo [SUCCESS] Configuração do CMake OK

echo.
echo [INFO] Testando compilação...

cmake --build . --config Debug --target CS2SkinChanger >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Falha na compilação
    cd ..
    goto :error
)

echo [SUCCESS] Compilação OK

cd ..

echo.
echo [INFO] Verificando arquivos de saída...

if not exist "build\Debug\CS2SkinChanger.exe" (
    echo [ERROR] Executável não foi criado
    goto :error
)

echo [SUCCESS] Executável criado com sucesso

echo.
echo [INFO] Testando carregamento de configuração...

REM Verificar se o JSON é válido
powershell -Command "try { Get-Content 'resources\config.json' | ConvertFrom-Json | Out-Null; Write-Host '[SUCCESS] JSON válido' } catch { Write-Host '[ERROR] JSON inválido'; exit 1 }" 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Arquivo de configuração inválido
    goto :error
)

echo.
echo [INFO] Verificando estrutura de diretórios...

if not exist "logs" mkdir logs
if not exist "build\Debug\resources" (
    mkdir "build\Debug\resources"
    copy "resources\*.*" "build\Debug\resources\" >nul 2>&1
)

echo [SUCCESS] Estrutura de diretórios OK

echo.
echo ========================================
echo TODOS OS TESTES PASSARAM!
echo ========================================
echo.
echo O projeto está pronto para uso.
echo.
echo Para executar:
echo   cd build\Debug
echo   CS2SkinChanger.exe
echo.
echo IMPORTANTE:
echo - Execute como Administrador
echo - Use apenas para fins educacionais
echo - Não use em servidores oficiais
echo.
goto :end

:error
echo.
echo ========================================
echo FALHA NOS TESTES!
echo ========================================
echo.
echo Verifique os erros acima e tente novamente.
echo.

:end
pause
