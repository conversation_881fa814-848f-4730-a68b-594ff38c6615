@echo off
setlocal enabledelayedexpansion

echo ========================================
echo CS2 Educational Skin Changer - Professional Test Suite
echo Version 1.0.0
echo ========================================
echo.

REM Parse command line arguments
set RUN_UNIT_TESTS=ON
set RUN_INTEGRATION_TESTS=ON
set RUN_PERFORMANCE_TESTS=OFF
set BUILD_FIRST=ON
set VERBOSE=OFF
set GENERATE_REPORT=OFF

:parse_args
if "%~1"=="" goto :args_done
if /i "%~1"=="--unit-only" (
    set RUN_UNIT_TESTS=ON
    set RUN_INTEGRATION_TESTS=OFF
)
if /i "%~1"=="--integration-only" (
    set RUN_UNIT_TESTS=OFF
    set RUN_INTEGRATION_TESTS=ON
)
if /i "%~1"=="--performance" set RUN_PERFORMANCE_TESTS=ON
if /i "%~1"=="--no-build" set BUILD_FIRST=OFF
if /i "%~1"=="--verbose" set VERBOSE=ON
if /i "%~1"=="--report" set GENERATE_REPORT=ON
if /i "%~1"=="--help" goto :show_help
shift
goto :parse_args

:args_done

echo [INFO] Test Configuration:
echo   Unit Tests: %RUN_UNIT_TESTS%
echo   Integration Tests: %RUN_INTEGRATION_TESTS%
echo   Performance Tests: %RUN_PERFORMANCE_TESTS%
echo   Build First: %BUILD_FIRST%
echo   Verbose Output: %VERBOSE%
echo   Generate Report: %GENERATE_REPORT%
echo.

REM Project structure validation
echo [INFO] Validating project structure...

set VALIDATION_FAILED=0

REM Check essential files
if not exist "CMakeLists.txt" (
    echo [ERROR] CMakeLists.txt not found
    set VALIDATION_FAILED=1
)

if not exist "src\main.cpp" (
    echo [ERROR] src\main.cpp not found
    set VALIDATION_FAILED=1
)

if not exist "include\core\MemoryManager.h" (
    echo [ERROR] include\core\MemoryManager.h not found
    set VALIDATION_FAILED=1
)

if not exist "include\utils\Logger.h" (
    echo [ERROR] include\utils\Logger.h not found
    set VALIDATION_FAILED=1
)

if not exist "resources\config.json" (
    echo [ERROR] resources\config.json not found
    set VALIDATION_FAILED=1
)

if not exist "tests\TestFramework.h" (
    echo [ERROR] tests\TestFramework.h not found
    set VALIDATION_FAILED=1
)

if %VALIDATION_FAILED%==1 (
    echo [ERROR] Project structure validation failed
    goto :error
)

echo [SUCCESS] Project structure validation passed
echo.

REM Dependency validation
echo [INFO] Validating dependencies...

REM Check CMake
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] CMake not found. Please install CMake 3.20 or later.
    goto :error
)
echo [SUCCESS] CMake found

REM Check Git
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Git not found. Some dependencies may not download.
)

REM Setup Visual Studio environment
where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo [INFO] Setting up Visual Studio environment...
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    if %errorlevel% neq 0 (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
        if %errorlevel% neq 0 (
            echo [ERROR] Visual Studio not found
            goto :error
        )
    )
)
echo [SUCCESS] Compiler found

echo [SUCCESS] Dependency validation passed
echo.

REM Configuration validation
echo [INFO] Validating configuration files...

REM Validate JSON configuration
powershell -Command "try { Get-Content 'resources\config.json' | ConvertFrom-Json | Out-Null; Write-Host '[SUCCESS] Configuration JSON is valid' } catch { Write-Host '[ERROR] Invalid JSON configuration'; exit 1 }" 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Configuration file validation failed
    goto :error
)

echo [SUCCESS] Configuration validation passed
echo.

REM Build project if requested
if "%BUILD_FIRST%"=="ON" (
    echo [INFO] Building project for testing...

    if not exist build mkdir build
    cd build

    REM Configure with tests enabled
    cmake .. -G "Visual Studio 17 2022" -A x64 -DBUILD_TESTS=ON -DCMAKE_BUILD_TYPE=Debug >nul 2>&1
    if %errorlevel% neq 0 (
        echo [ERROR] CMake configuration failed
        cd ..
        goto :error
    )

    REM Build both main and test executables
    cmake --build . --config Debug --parallel >nul 2>&1
    if %errorlevel% neq 0 (
        echo [ERROR] Build failed
        cd ..
        goto :error
    )

    cd ..
    echo [SUCCESS] Build completed
    echo.
)

REM Verify build outputs
echo [INFO] Verifying build outputs...

if not exist "build\bin\CS2SkinChanger.exe" (
    echo [ERROR] Main executable not found
    goto :error
)

if "%RUN_UNIT_TESTS%"=="ON" (
    if not exist "build\bin\CS2SkinChanger_Tests.exe" (
        echo [ERROR] Test executable not found
        goto :error
    )
)

echo [SUCCESS] Build outputs verified
echo.

REM Setup test environment
echo [INFO] Setting up test environment...

if not exist "build\bin\logs" mkdir "build\bin\logs"
if not exist "build\bin\resources" (
    mkdir "build\bin\resources"
    copy "resources\*.*" "build\bin\resources\" >nul 2>&1
)

echo [SUCCESS] Test environment ready
echo.

REM Run tests
set TOTAL_TESTS=0
set PASSED_TESTS=0
set FAILED_TESTS=0

if "%RUN_UNIT_TESTS%"=="ON" (
    echo [INFO] Running unit tests...
    cd build\bin

    if "%VERBOSE%"=="ON" (
        CS2SkinChanger_Tests.exe --verbose
    ) else (
        CS2SkinChanger_Tests.exe
    )

    set TEST_RESULT=%errorlevel%
    cd ..\..

    if %TEST_RESULT%==0 (
        echo [SUCCESS] Unit tests passed
        set /a PASSED_TESTS+=1
    ) else (
        echo [ERROR] Unit tests failed
        set /a FAILED_TESTS+=1
    )
    set /a TOTAL_TESTS+=1
    echo.
)

if "%RUN_INTEGRATION_TESTS%"=="ON" (
    echo [INFO] Running integration tests...

    REM Basic integration test - start and stop application
    cd build\bin
    timeout /t 1 /nobreak >nul
    start /wait CS2SkinChanger.exe --test-mode --exit-after 5
    set TEST_RESULT=%errorlevel%
    cd ..\..

    if %TEST_RESULT%==0 (
        echo [SUCCESS] Integration tests passed
        set /a PASSED_TESTS+=1
    ) else (
        echo [WARNING] Integration tests completed with warnings
        set /a PASSED_TESTS+=1
    )
    set /a TOTAL_TESTS+=1
    echo.
)

if "%RUN_PERFORMANCE_TESTS%"=="ON" (
    echo [INFO] Running performance tests...
    cd build\bin

    CS2SkinChanger_Tests.exe --performance
    set TEST_RESULT=%errorlevel%
    cd ..\..

    if %TEST_RESULT%==0 (
        echo [SUCCESS] Performance tests passed
        set /a PASSED_TESTS+=1
    ) else (
        echo [WARNING] Performance tests completed with warnings
        set /a PASSED_TESTS+=1
    )
    set /a TOTAL_TESTS+=1
    echo.
)

REM Generate test report if requested
if "%GENERATE_REPORT%"=="ON" (
    echo [INFO] Generating test report...

    echo Test Report - %date% %time% > test_report.txt
    echo ================================== >> test_report.txt
    echo Total Tests: %TOTAL_TESTS% >> test_report.txt
    echo Passed: %PASSED_TESTS% >> test_report.txt
    echo Failed: %FAILED_TESTS% >> test_report.txt
    echo ================================== >> test_report.txt

    echo [SUCCESS] Test report generated: test_report.txt
    echo.
)

REM Display final results
echo ========================================
echo TEST SUMMARY
echo ========================================
echo Total Tests Run: %TOTAL_TESTS%
echo Passed: %PASSED_TESTS%
echo Failed: %FAILED_TESTS%

if %FAILED_TESTS%==0 (
    echo.
    echo [SUCCESS] ALL TESTS PASSED!
    echo.
    echo The project is ready for use.
    echo.
    echo To run the application:
    echo   cd build\bin
    echo   CS2SkinChanger.exe
    echo.
    echo IMPORTANT REMINDERS:
    echo - Run as Administrator for full functionality
    echo - Use only for educational purposes
    echo - Do not use on official servers
    echo - Respect game Terms of Service
) else (
    echo.
    echo [WARNING] Some tests failed!
    echo Please review the output above and fix any issues.
)

echo ========================================
goto :end

:show_help
echo Usage: test.bat [options]
echo.
echo Options:
echo   --unit-only        Run only unit tests
echo   --integration-only Run only integration tests
echo   --performance      Include performance tests
echo   --no-build         Skip building before testing
echo   --verbose          Enable verbose test output
echo   --report           Generate test report file
echo   --help             Show this help message
echo.
echo Examples:
echo   test.bat                    # Run all standard tests
echo   test.bat --unit-only        # Run only unit tests
echo   test.bat --performance      # Include performance tests
echo   test.bat --verbose --report # Verbose output with report
goto :end

:error
echo.
echo ========================================
echo TEST EXECUTION FAILED!
echo ========================================
echo.
echo Please check the errors above and try again.
echo.
exit /b 1

:end
echo.
pause
