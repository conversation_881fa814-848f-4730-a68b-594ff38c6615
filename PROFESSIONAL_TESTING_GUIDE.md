# Guia de Testes Profissionais - CS2 Educational Skin Changer

## 🎯 Visão Geral

Este guia fornece instruções detalhadas para testar o CS2 Educational Skin Changer de forma profissional e abrangente.

## 🚀 Quick Start

### Teste Rápido
```bash
# Teste padrão completo
test.bat

# Apenas testes unitários
test.bat --unit-only

# Testes com relatório detalhado
test.bat --verbose --report
```

### Build e Teste
```bash
# Build profissional com testes
build.bat --tests --run-tests

# Build limpo com testes de performance
build.bat --clean --tests
test.bat --performance
```

## 📋 Tipos de Testes

### 1. Testes Unitários
**Objetivo**: Validar componentes individuais

**Componentes Testados**:
- MemoryManager
- PatternScanner
- SkinEngine
- ConfigManager
- Logger
- ErrorHandler

**Execução**:
```bash
test.bat --unit-only
```

**Critérios de Sucesso**:
- ✅ Todos os testes unitários passam
- ✅ Cobertura de código > 80%
- ✅ Sem vazamentos de memória
- ✅ Performance dentro dos limites

### 2. Testes de Integração
**Objetivo**: Validar interação entre componentes

**Cenários Testados**:
- Inicialização completa do sistema
- Carregamento de configurações
- Conexão com processo CS2 (simulado)
- Aplicação de skins
- Sistema de logging
- Tratamento de erros

**Execução**:
```bash
test.bat --integration-only
```

### 3. Testes de Performance
**Objetivo**: Validar performance e uso de recursos

**Métricas Avaliadas**:
- Tempo de inicialização < 2s
- Uso de memória < 50MB
- Tempo de scan de padrões < 5s
- Latência de aplicação de skin < 100ms

**Execução**:
```bash
test.bat --performance
```

## 🔧 Configuração de Ambiente de Teste

### Pré-requisitos
- Windows 10/11 (64-bit)
- Visual Studio 2019/2022
- CMake 3.20+
- Git (para dependências)
- Privilégios de Administrador

### Setup Automático
```bash
# O script de teste configura automaticamente:
# - Ambiente de compilação
# - Dependências
# - Diretórios de teste
# - Configurações
```

### Setup Manual
```bash
# 1. Configurar ambiente
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

# 2. Criar diretórios
mkdir build\test_results
mkdir build\coverage

# 3. Configurar CMake
cd build
cmake .. -DBUILD_TESTS=ON -DCMAKE_BUILD_TYPE=Debug

# 4. Compilar
cmake --build . --config Debug
```

## 📊 Análise de Resultados

### Interpretação de Saídas

#### Sucesso Total
```
========================================
TEST SUMMARY
========================================
Total Tests Run: 25
Passed: 25
Failed: 0

[SUCCESS] ALL TESTS PASSED!
```

#### Falhas Parciais
```
========================================
TEST SUMMARY
========================================
Total Tests Run: 25
Passed: 22
Failed: 3

[WARNING] Some tests failed!
```

### Logs Detalhados
- **Localização**: `build/bin/logs/`
- **Arquivos**:
  - `test_execution.log` - Log completo dos testes
  - `performance.log` - Métricas de performance
  - `memory_usage.log` - Análise de uso de memória

### Relatórios
```bash
# Gerar relatório completo
test.bat --report

# Localização: test_report.txt
```

## 🐛 Debugging de Falhas

### Testes Unitários Falhando

#### MemoryManager Tests
```bash
# Problemas comuns:
# - Privilégios insuficientes
# - Antivírus bloqueando
# - Processo CS2 não encontrado

# Soluções:
# 1. Executar como Administrador
# 2. Adicionar exceção no antivírus
# 3. Verificar nome do processo
```

#### PatternScanner Tests
```bash
# Problemas comuns:
# - Padrões desatualizados
# - Memória insuficiente
# - Timeout de scan

# Soluções:
# 1. Atualizar padrões
# 2. Aumentar timeout
# 3. Verificar uso de memória
```

### Testes de Integração Falhando

#### Inicialização
```bash
# Verificar:
# - Arquivos de configuração
# - Dependências
# - Permissões de arquivo
```

#### Conectividade
```bash
# Verificar:
# - Processo CS2 rodando
# - Privilégios de debug
# - Firewall/antivírus
```

### Testes de Performance Falhando

#### Tempo Limite Excedido
```bash
# Causas possíveis:
# - Hardware insuficiente
# - Outros processos consumindo recursos
# - Configuração de debug

# Soluções:
# 1. Fechar outros programas
# 2. Usar build Release
# 3. Ajustar limites de tempo
```

## 📈 Métricas de Qualidade

### Cobertura de Código
**Meta**: > 80%
```bash
# Gerar relatório de cobertura
cmake --build . --target coverage
```

### Complexidade Ciclomática
**Meta**: < 10 por função
```bash
# Análise com clang-tidy
build.bat --clang-tidy
```

### Vazamentos de Memória
**Meta**: Zero vazamentos
```bash
# Análise com AddressSanitizer
build.bat --debug
test.bat --unit-only
```

### Performance Benchmarks
| Componente | Tempo Limite | Uso Memória |
|------------|--------------|-------------|
| Inicialização | 2s | 10MB |
| Pattern Scan | 5s | 20MB |
| Skin Apply | 100ms | 1MB |
| UI Render | 16ms | 5MB |

## 🔄 Integração Contínua

### GitHub Actions (Exemplo)
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup MSVC
      uses: microsoft/setup-msbuild@v1
    - name: Run Tests
      run: |
        build.bat --tests
        test.bat --report
    - name: Upload Results
      uses: actions/upload-artifact@v3
      with:
        name: test-results
        path: test_report.txt
```

### Automação Local
```bash
# Script de CI local
@echo off
echo Running CI pipeline...

call build.bat --clean --tests
if %errorlevel% neq 0 exit /b 1

call test.bat --verbose --report
if %errorlevel% neq 0 exit /b 1

echo CI pipeline completed successfully!
```

## 📝 Relatórios de Teste

### Formato de Relatório
```
Test Report - 2024-01-01 12:00:00
==================================
Environment:
- OS: Windows 11 Pro
- Compiler: MSVC 19.29
- Build: Debug
- Commit: abc123

Results:
- Unit Tests: 20/20 PASSED
- Integration Tests: 5/5 PASSED
- Performance Tests: 3/3 PASSED

Performance Metrics:
- Startup Time: 1.2s (Target: <2s)
- Memory Usage: 35MB (Target: <50MB)
- Pattern Scan: 3.1s (Target: <5s)

Issues Found: None

Recommendations:
- All tests passing
- Performance within targets
- Ready for deployment
==================================
```

## 🎯 Melhores Práticas

### Antes de Testar
1. ✅ Fechar outros programas
2. ✅ Executar como Administrador
3. ✅ Verificar espaço em disco
4. ✅ Atualizar drivers
5. ✅ Desabilitar antivírus temporariamente

### Durante os Testes
1. ✅ Monitorar uso de recursos
2. ✅ Verificar logs em tempo real
3. ✅ Documentar falhas
4. ✅ Capturar screenshots de erros

### Após os Testes
1. ✅ Analisar relatórios
2. ✅ Arquivar resultados
3. ✅ Planejar correções
4. ✅ Atualizar documentação

## 🚨 Troubleshooting

### Problemas Comuns

#### "Access Denied"
```bash
# Solução: Executar como Administrador
# Verificar: UAC, antivírus, permissões
```

#### "Process Not Found"
```bash
# Solução: Verificar nome do processo
# Alternativa: Usar processo de teste
```

#### "Pattern Not Found"
```bash
# Solução: Atualizar padrões
# Verificar: Versão do CS2, offsets
```

#### "Build Failed"
```bash
# Solução: Verificar dependências
# Limpar: build.bat --clean
```

---

**Lembre-se**: Testes são fundamentais para garantir a qualidade e confiabilidade do software. Execute-os regularmente e mantenha-os atualizados!
