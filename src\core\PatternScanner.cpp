#include "core/PatternScanner.h"
#include "core/MemoryManager.h"
#include <iostream>
#include <algorithm>
#include <sstream>

PatternScanner::PatternScanner() {
    InitializeCS2Patterns();
}

void PatternScanner::AddPattern(const std::string& name, const std::string& pattern, 
                               const std::string& mask, int offset) {
    if (!ValidatePattern(pattern, mask)) {
        std::cerr << "[ERROR] Padrão inválido: " << name << std::endl;
        return;
    }
    
    PatternInfo info;
    info.name = name;
    info.pattern = pattern;
    info.mask = mask;
    info.offset = offset;
    info.address = 0;
    info.found = false;
    
    patterns[name] = info;
}

bool PatternScanner::RemovePattern(const std::string& name) {
    auto it = patterns.find(name);
    if (it != patterns.end()) {
        patterns.erase(it);
        return true;
    }
    return false;
}

void PatternScanner::ClearPatterns() {
    patterns.clear();
}

bool PatternScanner::ScanPatterns(MemoryManager* memManager) {
    if (!memManager || !memManager->IsProcessValid()) {
        std::cerr << "[ERROR] MemoryManager inválido" << std::endl;
        return false;
    }
    
    memoryManager = memManager;
    return ScanAllPatterns();
}

bool PatternScanner::ScanPattern(const std::string& name) {
    auto it = patterns.find(name);
    if (it == patterns.end() || !memoryManager) {
        return false;
    }
    
    PatternInfo& info = it->second;
    
    std::cout << "[INFO] Escaneando padrão: " << name << std::endl;
    
    uintptr_t address = memoryManager->FindPatternFirst(info.pattern, info.mask);
    
    if (address != 0) {
        info.address = address + info.offset;
        info.found = true;
        std::cout << "[SUCCESS] Padrão encontrado: " << name 
                  << " em 0x" << std::hex << info.address << std::dec << std::endl;
        return true;
    } else {
        info.found = false;
        std::cout << "[WARNING] Padrão não encontrado: " << name << std::endl;
        return false;
    }
}

bool PatternScanner::ScanAllPatterns() {
    if (!memoryManager) {
        return false;
    }
    
    std::cout << "[INFO] Iniciando scan de " << patterns.size() << " padrões..." << std::endl;
    
    size_t foundCount = 0;
    for (auto& [name, info] : patterns) {
        if (ScanPattern(name)) {
            foundCount++;
        }
    }
    
    std::cout << "[INFO] Scan concluído: " << foundCount << "/" << patterns.size() 
              << " padrões encontrados" << std::endl;
    
    return foundCount > 0;
}

uintptr_t PatternScanner::GetPatternAddress(const std::string& name) const {
    auto it = patterns.find(name);
    return (it != patterns.end() && it->second.found) ? it->second.address : 0;
}

bool PatternScanner::IsPatternFound(const std::string& name) const {
    auto it = patterns.find(name);
    return it != patterns.end() && it->second.found;
}

const PatternInfo* PatternScanner::GetPatternInfo(const std::string& name) const {
    auto it = patterns.find(name);
    return it != patterns.end() ? &it->second : nullptr;
}

std::vector<std::string> PatternScanner::GetFoundPatterns() const {
    std::vector<std::string> found;
    for (const auto& [name, info] : patterns) {
        if (info.found) {
            found.push_back(name);
        }
    }
    return found;
}

std::vector<std::string> PatternScanner::GetMissingPatterns() const {
    std::vector<std::string> missing;
    for (const auto& [name, info] : patterns) {
        if (!info.found) {
            missing.push_back(name);
        }
    }
    return missing;
}

size_t PatternScanner::GetFoundCount() const {
    return std::count_if(patterns.begin(), patterns.end(),
                        [](const auto& pair) { return pair.second.found; });
}

size_t PatternScanner::GetTotalCount() const {
    return patterns.size();
}

void PatternScanner::InitializeCS2Patterns() {
    // Adicionar padrões conhecidos do CS2
    // NOTA: Estes são padrões de exemplo e podem precisar ser atualizados
    
    // Padrão para LocalPlayer
    AddPattern(CS2Patterns::LOCAL_PLAYER,
               "\x8B\x0D\x00\x00\x00\x00\x83\xF9\xFF\x74\x07",
               "xx????xxxxx", 2);
    
    // Padrão para EntityList
    AddPattern(CS2Patterns::ENTITY_LIST,
               "\xBB\x00\x00\x00\x00\x83\xFF\x01\x0F\x8C\x00\x00\x00\x00\x3B\xF8",
               "x????xxxxx????xx", 1);
    
    // Padrão para WeaponData
    AddPattern(CS2Patterns::WEAPON_DATA,
               "\x8B\x35\x00\x00\x00\x00\x85\xF6\x0F\x84\x00\x00\x00\x00\x8B\x06",
               "xx????xxxx????xx", 2);
    
    // Padrão para PaintKit
    AddPattern(CS2Patterns::PAINT_KIT,
               "\x89\x86\x00\x00\x00\x00\x5F\x5E\x5B\x8B\xE5\x5D\xC3",
               "xx????xxxxxxx", 2);
    
    // Padrão para ItemDefinitionIndex
    AddPattern(CS2Patterns::ITEM_DEFINITION_INDEX,
               "\x66\x89\x86\x00\x00\x00\x00\x8B\x86\x00\x00\x00\x00",
               "xxx????xx????", 3);
    
    // Padrão para WearFloat
    AddPattern(CS2Patterns::WEAR_FLOAT,
               "\xF3\x0F\x11\x86\x00\x00\x00\x00\x8B\x86\x00\x00\x00\x00",
               "xxxx????xx????", 4);
    
    // Padrão para Seed
    AddPattern(CS2Patterns::SEED,
               "\x89\x86\x00\x00\x00\x00\x8B\x86\x00\x00\x00\x00\x85\xC0",
               "xx????xx????xx", 2);
    
    // Padrão para StatTrak
    AddPattern(CS2Patterns::STATTRAK,
               "\x89\x86\x00\x00\x00\x00\x8B\x86\x00\x00\x00\x00\x3D\x00\x00\x00\x00",
               "xx????xx????x????", 2);
    
    std::cout << "[INFO] Inicializados " << patterns.size() << " padrões do CS2" << std::endl;
}

std::vector<uint8_t> PatternScanner::HexStringToBytes(const std::string& hex) const {
    std::vector<uint8_t> bytes;
    
    for (size_t i = 0; i < hex.length(); i += 2) {
        std::string byteString = hex.substr(i, 2);
        uint8_t byte = static_cast<uint8_t>(strtol(byteString.c_str(), nullptr, 16));
        bytes.push_back(byte);
    }
    
    return bytes;
}

std::string PatternScanner::CreateMaskFromPattern(const std::string& pattern) const {
    std::string mask;
    
    for (size_t i = 0; i < pattern.length(); i += 2) {
        std::string byteString = pattern.substr(i, 2);
        if (byteString == "??") {
            mask += "?";
        } else {
            mask += "x";
        }
    }
    
    return mask;
}

bool PatternScanner::ValidatePattern(const std::string& pattern, const std::string& mask) const {
    if (pattern.empty() || mask.empty()) {
        return false;
    }
    
    // Verificar se o tamanho do padrão corresponde à máscara
    size_t expectedMaskSize = pattern.length();
    if (expectedMaskSize != mask.length()) {
        return false;
    }
    
    // Verificar se a máscara contém apenas 'x' e '?'
    for (char c : mask) {
        if (c != 'x' && c != '?') {
            return false;
        }
    }
    
    return true;
}

// Implementação dos padrões conhecidos
namespace CS2Patterns {
    const PatternData KNOWN_PATTERNS[] = {
        {LOCAL_PLAYER, "\x8B\x0D\x00\x00\x00\x00\x83\xF9\xFF\x74\x07", "xx????xxxxx", 2},
        {ENTITY_LIST, "\xBB\x00\x00\x00\x00\x83\xFF\x01\x0F\x8C\x00\x00\x00\x00\x3B\xF8", "x????xxxxx????xx", 1},
        {WEAPON_DATA, "\x8B\x35\x00\x00\x00\x00\x85\xF6\x0F\x84\x00\x00\x00\x00\x8B\x06", "xx????xxxx????xx", 2},
        {PAINT_KIT, "\x89\x86\x00\x00\x00\x00\x5F\x5E\x5B\x8B\xE5\x5D\xC3", "xx????xxxxxxx", 2},
        {ITEM_DEFINITION_INDEX, "\x66\x89\x86\x00\x00\x00\x00\x8B\x86\x00\x00\x00\x00", "xxx????xx????", 3},
        {WEAR_FLOAT, "\xF3\x0F\x11\x86\x00\x00\x00\x00\x8B\x86\x00\x00\x00\x00", "xxxx????xx????", 4},
        {SEED, "\x89\x86\x00\x00\x00\x00\x8B\x86\x00\x00\x00\x00\x85\xC0", "xx????xx????xx", 2},
        {STATTRAK, "\x89\x86\x00\x00\x00\x00\x8B\x86\x00\x00\x00\x00\x3D\x00\x00\x00\x00", "xx????xx????x????", 2}
    };
    
    const size_t PATTERN_COUNT = sizeof(KNOWN_PATTERNS) / sizeof(PatternData);
}
