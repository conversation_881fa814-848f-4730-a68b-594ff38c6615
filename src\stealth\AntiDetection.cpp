#include "stealth/AntiDetection.h"
#include <iostream>
#include <random>
#include <thread>
#include <chrono>
#include <tlhelp32.h>
#include <psapi.h>

AntiDetection::AntiDetection() {
    // Configuração padrão
    config.enableProcessHiding = true;
    config.enableWindowHiding = true;
    config.enableMemoryObfuscation = true;
    config.enableAPIHooking = true;
    config.enableRandomDelays = true;
    config.enableDebuggerDetection = true;
}

AntiDetection::~AntiDetection() {
    Shutdown();
}

bool AntiDetection::Initialize() {
    if (isInitialized) {
        return true;
    }
    
    Log("[INFO] Inicializando sistema anti-detecção...");
    
    // Verificar se estamos em um ambiente seguro
    if (IsDebuggerPresent()) {
        Alert("[WARNING] Debugger detectado!");
        // Em um ambiente real, poderia decidir não inicializar
    }
    
    if (IsVirtualMachine()) {
        Alert("[WARNING] Máquina virtual detectada!");
    }
    
    if (IsSandbox()) {
        Alert("[WARNING] Sandbox detectado!");
    }
    
    if (IsBlacklistedProcessRunning()) {
        Alert("[WARNING] Processo suspeito detectado!");
    }
    
    // Aplicar proteções conforme configuração
    bool success = true;
    
    if (config.enableProcessHiding) {
        success &= EnableProcessHiding();
    }
    
    if (config.enableWindowHiding) {
        success &= EnableWindowHiding();
    }
    
    if (config.enableAPIHooking) {
        success &= EnableAPIHooking();
    }
    
    // Iniciar monitoramento
    if (config.enableDebuggerDetection) {
        StartMonitoring();
    }
    
    isInitialized = success;
    
    if (isInitialized) {
        Log("[INFO] Sistema anti-detecção inicializado com sucesso");
    } else {
        Log("[ERROR] Falha ao inicializar sistema anti-detecção");
    }
    
    return isInitialized;
}

void AntiDetection::Shutdown() {
    if (!isInitialized) {
        return;
    }
    
    Log("[INFO] Finalizando sistema anti-detecção...");
    
    StopMonitoring();
    RemoveAPIHooks();
    
    isInitialized = false;
    Log("[INFO] Sistema anti-detecção finalizado");
}

bool AntiDetection::EnableProcessHiding() {
    Log("[INFO] Habilitando ocultação de processo...");
    
    // Em uma implementação real, aqui seria implementada a ocultação do processo
    // Isso pode envolver hooks no NtQuerySystemInformation ou outras técnicas
    
    return HideFromProcessList();
}

bool AntiDetection::EnableWindowHiding() {
    Log("[INFO] Habilitando ocultação de janela...");
    
    return HideWindowFromTaskbar();
}

bool AntiDetection::EnableMemoryObfuscation() {
    Log("[INFO] Habilitando ofuscação de memória...");
    
    // Implementação simplificada de ofuscação de memória
    // Em um caso real, seria mais complexo
    
    return true;
}

bool AntiDetection::EnableAPIHooking() {
    Log("[INFO] Habilitando hooks de API...");
    
    return InstallAPIHooks();
}

bool AntiDetection::IsDebuggerPresent() {
    // Verificação básica usando API do Windows
    if (::IsDebuggerPresent()) {
        return true;
    }
    
    // Verificação usando NtQueryInformationProcess
    HANDLE hProcess = GetCurrentProcess();
    BOOL isDebugged = FALSE;
    
    typedef NTSTATUS (NTAPI *pNtQueryInformationProcess)(
        HANDLE ProcessHandle,
        ULONG ProcessInformationClass,
        PVOID ProcessInformation,
        ULONG ProcessInformationLength,
        PULONG ReturnLength
    );
    
    HMODULE hNtdll = GetModuleHandle(L"ntdll.dll");
    if (hNtdll) {
        pNtQueryInformationProcess NtQueryInformationProcess = 
            (pNtQueryInformationProcess)GetProcAddress(hNtdll, "NtQueryInformationProcess");
        
        if (NtQueryInformationProcess) {
            NTSTATUS status = NtQueryInformationProcess(hProcess, 7, &isDebugged, sizeof(BOOL), nullptr);
            if (status == 0 && isDebugged) {
                return true;
            }
        }
    }
    
    return CheckForHardwareBreakpoints() || CheckForSoftwareBreakpoints();
}

bool AntiDetection::IsVirtualMachine() {
    // Verificar processos conhecidos de VMs
    for (const char* process : StealthUtils::VMWARE_PROCESSES) {
        if (StealthUtils::IsProcessRunning(process)) {
            return true;
        }
    }
    
    for (const char* process : StealthUtils::VIRTUALBOX_PROCESSES) {
        if (StealthUtils::IsProcessRunning(process)) {
            return true;
        }
    }
    
    return CheckForVMWareArtifacts() || CheckForVirtualBoxArtifacts();
}

bool AntiDetection::IsSandbox() {
    // Verificar processos conhecidos de sandbox
    for (const char* process : StealthUtils::SANDBOX_PROCESSES) {
        if (StealthUtils::IsProcessRunning(process)) {
            return true;
        }
    }
    
    return CheckForSandboxArtifacts();
}

bool AntiDetection::IsBlacklistedProcessRunning() {
    for (const std::string& process : config.processBlacklist) {
        if (StealthUtils::IsProcessRunning(process)) {
            Alert("[WARNING] Processo na blacklist detectado: " + process);
            return true;
        }
    }
    return false;
}

void AntiDetection::RandomDelay() {
    if (!config.enableRandomDelays) {
        return;
    }
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(config.minDelayMs, config.maxDelayMs);
    
    int delay = dis(gen);
    StealthUtils::AccurateDelay(delay);
}

void AntiDetection::StartMonitoring() {
    if (monitoringThread || debuggerCheckThread) {
        return;
    }
    
    shouldStopMonitoring = false;
    
    monitoringThread = CreateThread(nullptr, 0, MonitoringThreadProc, this, 0, nullptr);
    debuggerCheckThread = CreateThread(nullptr, 0, DebuggerCheckThreadProc, this, 0, nullptr);
    
    Log("[INFO] Monitoramento iniciado");
}

void AntiDetection::StopMonitoring() {
    shouldStopMonitoring = true;
    
    if (monitoringThread) {
        WaitForSingleObject(monitoringThread, 5000);
        CloseHandle(monitoringThread);
        monitoringThread = nullptr;
    }
    
    if (debuggerCheckThread) {
        WaitForSingleObject(debuggerCheckThread, 5000);
        CloseHandle(debuggerCheckThread);
        debuggerCheckThread = nullptr;
    }
    
    Log("[INFO] Monitoramento parado");
}

std::string AntiDetection::GetStatusReport() const {
    std::string report = "=== Status do Sistema Anti-Detecção ===\n";
    report += "Inicializado: " + std::string(isInitialized ? "Sim" : "Não") + "\n";
    report += "Ocultação de Processo: " + std::string(config.enableProcessHiding ? "Ativa" : "Inativa") + "\n";
    report += "Ocultação de Janela: " + std::string(config.enableWindowHiding ? "Ativa" : "Inativa") + "\n";
    report += "Hooks de API: " + std::string(config.enableAPIHooking ? "Ativo" : "Inativo") + "\n";
    report += "Monitoramento: " + std::string((monitoringThread != nullptr) ? "Ativo" : "Inativo") + "\n";
    report += "Hooks Ativos: " + std::to_string(activeHooks.size()) + "\n";
    return report;
}

bool AntiDetection::IsProtectionActive() const {
    return isInitialized && (monitoringThread != nullptr || !activeHooks.empty());
}

bool AntiDetection::HideFromProcessList() {
    // Implementação simplificada
    // Em um caso real, seria necessário hook no NtQuerySystemInformation
    Log("[INFO] Tentando ocultar processo da lista de processos");
    return true;
}

bool AntiDetection::HideWindowFromTaskbar() {
    // Implementação simplificada
    // Em um caso real, seria necessário modificar propriedades da janela
    Log("[INFO] Tentando ocultar janela da barra de tarefas");
    return true;
}

bool AntiDetection::InstallAPIHooks() {
    Log("[INFO] Instalando hooks de API...");
    
    // Exemplo de hook (implementação simplificada)
    HookInfo hookInfo;
    if (InstallHook("ntdll.dll", "NtQuerySystemInformation", 
                   (void*)HookedNtQuerySystemInformation, hookInfo)) {
        activeHooks.push_back(hookInfo);
        Log("[INFO] Hook instalado: NtQuerySystemInformation");
    }
    
    return !activeHooks.empty();
}

void AntiDetection::RemoveAPIHooks() {
    Log("[INFO] Removendo hooks de API...");
    
    for (auto& hook : activeHooks) {
        RemoveHook(hook);
    }
    
    activeHooks.clear();
}

bool AntiDetection::CheckForHardwareBreakpoints() {
    // Verificar registradores de debug
    CONTEXT context;
    context.ContextFlags = CONTEXT_DEBUG_REGISTERS;
    
    if (GetThreadContext(GetCurrentThread(), &context)) {
        return (context.Dr0 != 0 || context.Dr1 != 0 || 
                context.Dr2 != 0 || context.Dr3 != 0);
    }
    
    return false;
}

bool AntiDetection::CheckForSoftwareBreakpoints() {
    // Verificar por instruções INT3 (0xCC) no código
    // Implementação simplificada
    return false;
}

bool AntiDetection::CheckForVMWareArtifacts() {
    // Verificar chaves de registro específicas do VMware
    for (const char* key : StealthUtils::VMWARE_REGISTRY_KEYS) {
        if (StealthUtils::RegistryKeyExists(HKEY_LOCAL_MACHINE, key)) {
            return true;
        }
    }
    return false;
}

bool AntiDetection::CheckForVirtualBoxArtifacts() {
    // Verificar chaves de registro específicas do VirtualBox
    for (const char* key : StealthUtils::VIRTUALBOX_REGISTRY_KEYS) {
        if (StealthUtils::RegistryKeyExists(HKEY_LOCAL_MACHINE, key)) {
            return true;
        }
    }
    return false;
}

bool AntiDetection::CheckForSandboxArtifacts() {
    // Verificar por artefatos comuns de sandbox
    // Implementação simplificada
    return false;
}

DWORD WINAPI AntiDetection::MonitoringThreadProc(LPVOID lpParameter) {
    AntiDetection* instance = static_cast<AntiDetection*>(lpParameter);
    
    while (!instance->shouldStopMonitoring) {
        // Verificar por processos suspeitos
        if (instance->IsBlacklistedProcessRunning()) {
            instance->Alert("[ALERT] Processo suspeito detectado durante monitoramento!");
        }
        
        // Delay antes da próxima verificação
        Sleep(5000); // 5 segundos
    }
    
    return 0;
}

DWORD WINAPI AntiDetection::DebuggerCheckThreadProc(LPVOID lpParameter) {
    AntiDetection* instance = static_cast<AntiDetection*>(lpParameter);
    
    while (!instance->shouldStopMonitoring) {
        // Verificar por debuggers
        if (instance->IsDebuggerPresent()) {
            instance->Alert("[ALERT] Debugger detectado durante monitoramento!");
        }
        
        // Delay antes da próxima verificação
        Sleep(2000); // 2 segundos
    }
    
    return 0;
}

void AntiDetection::Log(const std::string& message) const {
    if (logCallback) {
        logCallback(message);
    } else {
        std::cout << message << std::endl;
    }
}

void AntiDetection::Alert(const std::string& message) const {
    if (alertCallback) {
        alertCallback(message);
    } else {
        std::cerr << message << std::endl;
    }
}

bool AntiDetection::InstallHook(const std::string& moduleName, const std::string& functionName, 
                               void* hookFunction, HookInfo& hookInfo) {
    // Implementação simplificada de hook
    // Em um caso real, seria necessário usar bibliotecas como Microsoft Detours
    // ou implementar manualmente o hooking
    
    HMODULE hModule = GetModuleHandleA(moduleName.c_str());
    if (!hModule) {
        return false;
    }
    
    void* originalFunction = GetProcAddress(hModule, functionName.c_str());
    if (!originalFunction) {
        return false;
    }
    
    hookInfo.originalFunction = originalFunction;
    hookInfo.hookFunction = hookFunction;
    hookInfo.functionName = functionName;
    hookInfo.isActive = true;
    
    // Aqui seria implementado o hook real
    Log("[INFO] Hook simulado instalado para: " + functionName);
    
    return true;
}

bool AntiDetection::RemoveHook(HookInfo& hookInfo) {
    if (!hookInfo.isActive) {
        return false;
    }
    
    // Aqui seria removido o hook real
    Log("[INFO] Hook removido para: " + hookInfo.functionName);
    
    hookInfo.isActive = false;
    return true;
}

// Implementação dos hooks (simplificada)
NTSTATUS NTAPI AntiDetection::HookedNtQuerySystemInformation(
    ULONG SystemInformationClass,
    PVOID SystemInformation,
    ULONG SystemInformationLength,
    PULONG ReturnLength) {
    
    // Em uma implementação real, aqui seria filtrada a informação
    // para ocultar o processo da lista
    
    // Por enquanto, apenas um placeholder
    return 0;
}

BOOL WINAPI AntiDetection::HookedEnumWindows(WNDENUMPROC lpEnumFunc, LPARAM lParam) {
    // Hook para ocultar janelas
    return TRUE;
}

HWND WINAPI AntiDetection::HookedFindWindow(LPCSTR lpClassName, LPCSTR lpWindowName) {
    // Hook para impedir que nossa janela seja encontrada
    return nullptr;
}
