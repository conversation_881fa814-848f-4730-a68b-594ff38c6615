@echo off
setlocal enabledelayedexpansion

echo ========================================
echo CS2 Educational Skin Changer - Professional Build Script
echo Version 1.0.0
echo ========================================
echo.

REM Parse command line arguments
set BUILD_TYPE=Release
set BUILD_TESTS=ON
set USE_REAL_IMGUI=OFF
set ENABLE_CLANG_TIDY=OFF
set CLEAN_BUILD=OFF
set RUN_TESTS=OFF
set PACKAGE=OFF

:parse_args
if "%~1"=="" goto :args_done
if /i "%~1"=="--debug" set BUILD_TYPE=Debug
if /i "%~1"=="--release" set BUILD_TYPE=Release
if /i "%~1"=="--no-tests" set BUILD_TESTS=OFF
if /i "%~1"=="--tests" set BUILD_TESTS=ON
if /i "%~1"=="--real-imgui" set USE_REAL_IMGUI=ON
if /i "%~1"=="--clang-tidy" set ENABLE_CLANG_TIDY=ON
if /i "%~1"=="--clean" set CLEAN_BUILD=ON
if /i "%~1"=="--run-tests" set RUN_TESTS=ON
if /i "%~1"=="--package" set PACKAGE=ON
if /i "%~1"=="--help" goto :show_help
shift
goto :parse_args

:args_done

echo [INFO] Build Configuration:
echo   Build Type: %BUILD_TYPE%
echo   Build Tests: %BUILD_TESTS%
echo   Use Real ImGui: %USE_REAL_IMGUI%
echo   Enable Clang-Tidy: %ENABLE_CLANG_TIDY%
echo   Clean Build: %CLEAN_BUILD%
echo   Run Tests: %RUN_TESTS%
echo   Package: %PACKAGE%
echo.

REM Check prerequisites
echo [INFO] Checking prerequisites...

REM Check CMake
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] CMake not found. Please install CMake 3.20 or later.
    goto :error
)

REM Check Git (for FetchContent)
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Git not found. Some dependencies may not download properly.
)

REM Setup Visual Studio environment
where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo [INFO] Setting up Visual Studio environment...
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    if %errorlevel% neq 0 (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
        if %errorlevel% neq 0 (
            call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
            if %errorlevel% neq 0 (
                call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
                if %errorlevel% neq 0 (
                    echo [ERROR] Visual Studio not found. Please install Visual Studio 2019 or later.
                    goto :error
                )
            )
        )
    )
)

echo [SUCCESS] Prerequisites check passed
echo.

REM Clean build if requested
if "%CLEAN_BUILD%"=="ON" (
    echo [INFO] Cleaning previous build...
    if exist build rmdir /s /q build
    echo [SUCCESS] Build directory cleaned
    echo.
)

REM Create and enter build directory
echo [INFO] Setting up build directory...
if not exist build mkdir build
cd build

REM Configure project with CMake
echo [INFO] Configuring project with CMake...
set CMAKE_ARGS=-G "Visual Studio 17 2022" -A x64
set CMAKE_ARGS=%CMAKE_ARGS% -DCMAKE_BUILD_TYPE=%BUILD_TYPE%
set CMAKE_ARGS=%CMAKE_ARGS% -DBUILD_TESTS=%BUILD_TESTS%
set CMAKE_ARGS=%CMAKE_ARGS% -DUSE_REAL_IMGUI=%USE_REAL_IMGUI%
set CMAKE_ARGS=%CMAKE_ARGS% -DENABLE_CLANG_TIDY=%ENABLE_CLANG_TIDY%

echo [DEBUG] CMake command: cmake .. %CMAKE_ARGS%
cmake .. %CMAKE_ARGS%
if %errorlevel% neq 0 (
    echo [ERROR] CMake configuration failed.
    goto :error
)

echo [SUCCESS] Project configured successfully
echo.

REM Build project
echo [INFO] Building project (%BUILD_TYPE% configuration)...
cmake --build . --config %BUILD_TYPE% --parallel
if %errorlevel% neq 0 (
    echo [ERROR] Build failed.
    goto :error
)

echo [SUCCESS] Build completed successfully!
echo.

REM Run tests if requested
if "%RUN_TESTS%"=="ON" (
    if "%BUILD_TESTS%"=="ON" (
        echo [INFO] Running tests...
        ctest --config %BUILD_TYPE% --output-on-failure
        if %errorlevel% neq 0 (
            echo [WARNING] Some tests failed.
        ) else (
            echo [SUCCESS] All tests passed!
        )
        echo.
    ) else (
        echo [WARNING] Tests not built, skipping test execution.
        echo.
    )
)

REM Package if requested
if "%PACKAGE%"=="ON" (
    echo [INFO] Creating package...
    cmake --build . --config %BUILD_TYPE% --target package
    if %errorlevel% neq 0 (
        echo [WARNING] Package creation failed.
    ) else (
        echo [SUCCESS] Package created successfully!
    )
    echo.
)

REM Display results
echo ========================================
echo BUILD SUMMARY
echo ========================================
echo Build Type: %BUILD_TYPE%
echo Executable: build\bin\CS2SkinChanger.exe
if "%BUILD_TESTS%"=="ON" (
    echo Test Executable: build\bin\CS2SkinChanger_Tests.exe
)
echo Resources: build\bin\resources\
echo Logs: build\bin\logs\
echo.
echo To run the application:
echo   cd build\bin
echo   CS2SkinChanger.exe
echo.
echo IMPORTANT: Run as Administrator for full functionality
echo ========================================

goto :end

:show_help
echo Usage: build.bat [options]
echo.
echo Options:
echo   --debug          Build in Debug mode (default: Release)
echo   --release        Build in Release mode
echo   --tests          Build tests (default: ON)
echo   --no-tests       Don't build tests
echo   --real-imgui     Use real ImGui implementation
echo   --clang-tidy     Enable clang-tidy analysis
echo   --clean          Clean build directory before building
echo   --run-tests      Run tests after building
echo   --package        Create installation package
echo   --help           Show this help message
echo.
echo Examples:
echo   build.bat                           # Standard release build
echo   build.bat --debug --tests           # Debug build with tests
echo   build.bat --clean --run-tests       # Clean build and run tests
echo   build.bat --release --package       # Release build with packaging
goto :end

:error
echo.
echo [ERROR] Build failed!
echo Check the output above for details.
cd ..
exit /b 1

:end
cd ..
echo.
pause
