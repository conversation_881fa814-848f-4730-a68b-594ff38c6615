@echo off
echo ========================================
echo CS2 Educational Skin Changer - Build Script
echo ========================================
echo.

REM Verificar se o CMake está instalado
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] CMake não encontrado. Por favor, instale o CMake.
    pause
    exit /b 1
)

REM Verificar se o Visual Studio está disponível
where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo [INFO] Tentando configurar ambiente do Visual Studio...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    if %errorlevel% neq 0 (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
        if %errorlevel% neq 0 (
            echo [ERROR] Visual Studio não encontrado. Por favor, instale o Visual Studio.
            pause
            exit /b 1
        )
    )
)

echo [INFO] Criando diretório de build...
if not exist build mkdir build
cd build

echo [INFO] Configurando projeto com CMake...
cmake .. -G "Visual Studio 17 2022" -A x64
if %errorlevel% neq 0 (
    echo [ERROR] Falha na configuração do CMake.
    pause
    exit /b 1
)

echo [INFO] Compilando projeto (Release)...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo [ERROR] Falha na compilação.
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Compilação concluída com sucesso!
echo [INFO] Executável criado em: build\Release\CS2SkinChanger.exe
echo.

REM Copiar recursos necessários
echo [INFO] Copiando recursos...
if not exist Release\resources mkdir Release\resources
copy ..\resources\*.* Release\resources\ >nul 2>&1

if not exist Release\logs mkdir Release\logs

echo [INFO] Build completo!
echo.
echo Para executar o programa:
echo   cd build\Release
echo   CS2SkinChanger.exe
echo.
echo AVISO: Execute como Administrador para funcionalidade completa.
echo.

pause
