# Guia de Uso - CS2 Educational Skin Changer

## ⚠️ LEIA ANTES DE USAR

**ESTE SOFTWARE É EXCLUSIVAMENTE EDUCACIONAL!**

- ❌ **NÃO use em servidores oficiais do CS2**
- ❌ **NÃO use para obter vantagens competitivas**
- ❌ **Pode resultar em VAC Ban permanente**
- ✅ **Use apenas para aprender sobre engenharia reversa**
- ✅ **Use apenas em ambiente offline/local**

## Instalação e Configuração

### 1. Pré-requisitos
```
- Windows 10/11 (64-bit)
- Visual Studio 2019/2022
- CMake 3.20+
- Privilégios de Administrador
```

### 2. Compilação
```bash
# Método 1: Script automático
build.bat

# Método 2: Manual
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

### 3. Teste da Instalação
```bash
test.bat
```

## Como Usar

### 1. Preparação
1. **Feche o CS2** se estiver rodando
2. **Execute como Administrador**
3. **Configure** o arquivo `resources/config.json`
4. **Inicie** o CS2 em modo offline

### 2. Execução
```bash
cd build/Release
CS2SkinChanger.exe
```

### 3. Interface Principal

#### Status Panel
- **CS2 Status**: Mostra se o processo foi detectado
- **Memory Status**: Status da conexão com a memória
- **Stealth Status**: Status das proteções anti-detecção

#### Weapon Selector
- Selecione a categoria de arma (Rifle, Pistol, Knife, etc.)
- Escolha a arma específica
- Visualize skins disponíveis

#### Skin Configuration
- **Paint Kit**: ID da skin
- **Wear Float**: Desgaste (0.0 = Factory New, 1.0 = Battle-Scarred)
- **Seed**: Padrão da skin
- **StatTrak**: Contador de kills (-1 = desabilitado)
- **Quality**: Qualidade do item (0-7)
- **Custom Name**: Nome personalizado

### 4. Aplicação de Skins
1. Configure os parâmetros desejados
2. Clique em "Apply Skin"
3. Entre no jogo para ver o resultado
4. Use "Remove Skin" para reverter

## Configurações Avançadas

### config.json
```json
{
  "target": {
    "processName": "cs2.exe",
    "updateInterval": 100
  },
  "stealth": {
    "enableProcessHiding": true,
    "enableAPIHooking": true,
    "enableDebuggerDetection": true
  },
  "skins": {
    "autoApply": false,
    "defaultQuality": 4,
    "defaultWear": 0.1
  }
}
```

### Stealth Settings
- **Process Hiding**: Oculta o processo de ferramentas de monitoramento
- **API Hooking**: Intercepta chamadas de sistema
- **Debugger Detection**: Detecta debuggers e ferramentas de análise
- **Memory Obfuscation**: Ofusca regiões de memória críticas

## Troubleshooting

### Problemas Comuns

#### "Processo não encontrado"
```
Soluções:
1. Verifique se o CS2 está rodando
2. Execute como Administrador
3. Verifique o nome do processo em config.json
4. Desabilite antivírus temporariamente
```

#### "Acesso negado"
```
Soluções:
1. Execute como Administrador
2. Desabilite UAC temporariamente
3. Adicione exceção no Windows Defender
4. Verifique permissões de arquivo
```

#### "Padrões não encontrados"
```
Soluções:
1. Atualize os padrões de memória
2. Verifique versão do CS2
3. Execute scan manual de padrões
4. Consulte logs para detalhes
```

#### "Interface não aparece"
```
Soluções:
1. Atualize drivers gráficos
2. Execute em modo compatibilidade
3. Verifique resolução da tela
4. Reinstale Visual C++ Redistributable
```

### Logs e Diagnóstico

#### Localização dos Logs
```
logs/skinchanger.log
logs/memory.log
logs/stealth.log
```

#### Níveis de Log
- **ERROR**: Erros críticos
- **WARNING**: Avisos importantes
- **INFO**: Informações gerais
- **DEBUG**: Detalhes técnicos

#### Exemplo de Log
```
[2024-01-01 12:00:00] [INFO] Inicializando CS2 Skin Changer...
[2024-01-01 12:00:01] [INFO] Conectado ao processo cs2.exe (PID: 1234)
[2024-01-01 12:00:02] [SUCCESS] Padrão encontrado: LocalPlayer em 0x12345678
[2024-01-01 12:00:03] [INFO] Skin aplicada para arma 7 (AK-47)
```

## Conceitos Técnicos

### Memory Patterns
```cpp
// Exemplo de padrão para LocalPlayer
Pattern: "\x8B\x0D\x00\x00\x00\x00\x83\xF9\xFF\x74\x07"
Mask:    "xx????xxxxx"
Offset:  +2
```

### Skin Structure
```cpp
struct SkinInfo {
    int paintKit;        // ID da skin
    float wearFloat;     // 0.0-1.0
    int seed;           // Padrão
    int statTrak;       // Contador
    int quality;        // 0-7
    char customName[32]; // Nome
};
```

### Memory Offsets
```cpp
// Offsets típicos (podem mudar)
LocalPlayer = 0x12345678
EntityList = 0x87654321
PaintKit = 0x1234
WearFloat = 0x1238
Seed = 0x123C
```

## Segurança e Detecção

### Técnicas Anti-Detecção Implementadas
1. **Process Hiding**: Oculta da lista de processos
2. **API Hooking**: Intercepta chamadas suspeitas
3. **Memory Obfuscation**: Ofusca código em memória
4. **Debugger Detection**: Detecta ferramentas de debug
5. **VM Detection**: Detecta máquinas virtuais
6. **Timing Attacks**: Usa delays aleatórios

### Limitações
- Pode ser detectado por anti-cheats avançados
- Padrões podem ficar obsoletos
- Funciona apenas no Windows
- Requer privilégios elevados

## Aspectos Legais e Éticos

### Uso Permitido
✅ Pesquisa em segurança de software
✅ Educação em engenharia reversa
✅ Desenvolvimento de anti-cheats
✅ Análise de malware
✅ Testes em ambiente controlado

### Uso Proibido
❌ Trapaça em jogos online
❌ Venda ou distribuição comercial
❌ Bypass de sistemas de segurança
❌ Violação de ToS de jogos
❌ Atividades ilegais

### Responsabilidades
- O usuário é responsável pelo uso
- Desenvolvedores não se responsabilizam por banimentos
- Use por sua própria conta e risco
- Respeite leis locais e ToS

## Recursos Adicionais

### Documentação Técnica
- [Memory Management](docs/memory.md)
- [Pattern Scanning](docs/patterns.md)
- [Anti-Detection](docs/stealth.md)
- [API Reference](docs/api.md)

### Ferramentas Úteis
- **Cheat Engine**: Análise de memória
- **x64dbg**: Debugging avançado
- **Process Hacker**: Monitor de processos
- **HxD**: Editor hexadecimal

### Comunidade
- GitHub Issues para bugs
- Discussions para dúvidas
- Wiki para documentação
- Discord para chat (educacional)

## Atualizações

### Versioning
```
Major.Minor.Patch
1.0.0 - Release inicial
1.1.0 - Novos recursos
1.0.1 - Correções de bugs
```

### Changelog
Consulte `CHANGELOG.md` para histórico completo.

### Roadmap
- [ ] Suporte para mais jogos
- [ ] Interface melhorada
- [ ] Mais técnicas anti-detecção
- [ ] Documentação expandida

---

**Lembre-se: Este projeto é para fins educacionais. Use com responsabilidade e ética!**
