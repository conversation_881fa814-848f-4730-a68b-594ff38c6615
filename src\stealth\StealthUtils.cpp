#include "stealth/AntiDetection.h"
#include <tlhelp32.h>
#include <psapi.h>
#include <winreg.h>
#include <chrono>
#include <thread>
#include <random>
#include <algorithm>

namespace StealthUtils {

bool IsProcessRunning(const std::string& processName) {
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return false;
    }
    
    PROCESSENTRY32 processEntry;
    processEntry.dwSize = sizeof(PROCESSENTRY32);
    
    bool found = false;
    if (Process32First(snapshot, &processEntry)) {
        do {
            std::string currentProcess = processEntry.szExeFile;
            std::transform(currentProcess.begin(), currentProcess.end(), 
                         currentProcess.begin(), ::tolower);
            
            std::string targetProcess = processName;
            std::transform(targetProcess.begin(), targetProcess.end(), 
                         targetProcess.begin(), ::tolower);
            
            if (currentProcess == targetProcess) {
                found = true;
                break;
            }
        } while (Process32Next(snapshot, &processEntry));
    }
    
    CloseHandle(snapshot);
    return found;
}

bool RegistryKeyExists(HKEY hKey, const std::string& subKey) {
    HKEY hTestKey;
    LONG result = RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hTestKey);
    
    if (result == ERROR_SUCCESS) {
        RegCloseKey(hTestKey);
        return true;
    }
    
    return false;
}

std::vector<std::string> GetRunningProcesses() {
    std::vector<std::string> processes;
    
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return processes;
    }
    
    PROCESSENTRY32 processEntry;
    processEntry.dwSize = sizeof(PROCESSENTRY32);
    
    if (Process32First(snapshot, &processEntry)) {
        do {
            processes.push_back(processEntry.szExeFile);
        } while (Process32Next(snapshot, &processEntry));
    }
    
    CloseHandle(snapshot);
    return processes;
}

bool IsFilePresent(const std::string& filePath) {
    DWORD attributes = GetFileAttributesA(filePath.c_str());
    return (attributes != INVALID_FILE_ATTRIBUTES && 
            !(attributes & FILE_ATTRIBUTE_DIRECTORY));
}

void AccurateDelay(int milliseconds) {
    auto start = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::milliseconds(milliseconds);
    
    while (std::chrono::high_resolution_clock::now() - start < duration) {
        std::this_thread::yield();
    }
}

uint64_t GetHighResolutionTime() {
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
}

bool IsAddressValid(void* address) {
    if (address == nullptr) {
        return false;
    }
    
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(address, &mbi, sizeof(mbi)) == 0) {
        return false;
    }
    
    return (mbi.State == MEM_COMMIT);
}

bool IsMemoryReadable(void* address, size_t size) {
    if (!IsAddressValid(address)) {
        return false;
    }
    
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(address, &mbi, sizeof(mbi)) == 0) {
        return false;
    }
    
    return (mbi.Protect & (PAGE_READONLY | PAGE_READWRITE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE));
}

bool IsMemoryWritable(void* address, size_t size) {
    if (!IsAddressValid(address)) {
        return false;
    }
    
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(address, &mbi, sizeof(mbi)) == 0) {
        return false;
    }
    
    return (mbi.Protect & (PAGE_READWRITE | PAGE_EXECUTE_READWRITE));
}

std::string ObfuscateString(const std::string& input) {
    std::string obfuscated = input;
    
    // Simples XOR com chave fixa (em produção, usar algo mais sofisticado)
    const uint8_t key = 0xAA;
    
    for (char& c : obfuscated) {
        c ^= key;
    }
    
    return obfuscated;
}

std::string DeobfuscateString(const std::string& obfuscated) {
    // XOR é reversível com a mesma chave
    return ObfuscateString(obfuscated);
}

std::string GetCPUBrand() {
    int cpuInfo[4] = {0};
    char brand[49] = {0};
    
    // Obter informações da CPU usando CPUID
    __cpuid(cpuInfo, 0x80000002);
    memcpy(brand, cpuInfo, sizeof(cpuInfo));
    
    __cpuid(cpuInfo, 0x80000003);
    memcpy(brand + 16, cpuInfo, sizeof(cpuInfo));
    
    __cpuid(cpuInfo, 0x80000004);
    memcpy(brand + 32, cpuInfo, sizeof(cpuInfo));
    
    return std::string(brand);
}

std::string GetMotherboardSerial() {
    std::string serial;
    
    // Tentar obter serial da placa-mãe via WMI ou registro
    HKEY hKey;
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, 
                     "HARDWARE\\DESCRIPTION\\System\\BIOS", 
                     0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        
        char buffer[256];
        DWORD bufferSize = sizeof(buffer);
        
        if (RegQueryValueExA(hKey, "SystemSerialNumber", nullptr, nullptr, 
                           (LPBYTE)buffer, &bufferSize) == ERROR_SUCCESS) {
            serial = buffer;
        }
        
        RegCloseKey(hKey);
    }
    
    return serial;
}

std::vector<std::string> GetInstalledSoftware() {
    std::vector<std::string> software;
    
    const char* uninstallKey = "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall";
    HKEY hKey;
    
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, uninstallKey, 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        DWORD index = 0;
        char subKeyName[256];
        DWORD subKeyNameSize = sizeof(subKeyName);
        
        while (RegEnumKeyExA(hKey, index, subKeyName, &subKeyNameSize, 
                           nullptr, nullptr, nullptr, nullptr) == ERROR_SUCCESS) {
            
            HKEY hSubKey;
            if (RegOpenKeyExA(hKey, subKeyName, 0, KEY_READ, &hSubKey) == ERROR_SUCCESS) {
                char displayName[256];
                DWORD displayNameSize = sizeof(displayName);
                
                if (RegQueryValueExA(hSubKey, "DisplayName", nullptr, nullptr, 
                                   (LPBYTE)displayName, &displayNameSize) == ERROR_SUCCESS) {
                    software.push_back(displayName);
                }
                
                RegCloseKey(hSubKey);
            }
            
            index++;
            subKeyNameSize = sizeof(subKeyName);
        }
        
        RegCloseKey(hKey);
    }
    
    return software;
}

} // namespace StealthUtils
