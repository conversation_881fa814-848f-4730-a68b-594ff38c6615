#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <functional>
#include <mutex>
#include <any>
#include <typeinfo>

// Forward declaration for JSON
namespace nlohmann {
    class json;
}

class ConfigValue {
private:
    std::any value_;
    std::string description_;
    bool isRequired_;
    std::function<bool(const std::any&)> validator_;

public:
    template<typename T>
    ConfigValue(const T& defaultValue, const std::string& description = "", bool required = false)
        : value_(defaultValue), description_(description), isRequired_(required) {}
    
    template<typename T>
    ConfigValue(const T& defaultValue, const std::string& description, 
               std::function<bool(const T&)> validator, bool required = false)
        : value_(defaultValue), description_(description), isRequired_(required) {
        validator_ = [validator](const std::any& val) {
            try {
                return validator(std::any_cast<T>(val));
            } catch (const std::bad_any_cast&) {
                return false;
            }
        };
    }
    
    template<typename T>
    T Get() const {
        try {
            return std::any_cast<T>(value_);
        } catch (const std::bad_any_cast&) {
            throw std::runtime_error("Invalid type cast for config value");
        }
    }
    
    template<typename T>
    bool Set(const T& newValue) {
        if (validator_ && !validator_(newValue)) {
            return false;
        }
        value_ = newValue;
        return true;
    }
    
    const std::string& GetDescription() const { return description_; }
    bool IsRequired() const { return isRequired_; }
    bool Validate() const { return !validator_ || validator_(value_); }
    
    std::string GetTypeName() const { return value_.type().name(); }
};

class ConfigSection {
private:
    std::string name_;
    std::string description_;
    std::unordered_map<std::string, std::unique_ptr<ConfigValue>> values_;
    mutable std::mutex mutex_;

public:
    ConfigSection(const std::string& name, const std::string& description = "");
    
    template<typename T>
    void AddValue(const std::string& key, const T& defaultValue, 
                 const std::string& description = "", bool required = false) {
        std::lock_guard<std::mutex> lock(mutex_);
        values_[key] = std::make_unique<ConfigValue>(defaultValue, description, required);
    }
    
    template<typename T>
    void AddValue(const std::string& key, const T& defaultValue, 
                 const std::string& description, std::function<bool(const T&)> validator, 
                 bool required = false) {
        std::lock_guard<std::mutex> lock(mutex_);
        values_[key] = std::make_unique<ConfigValue>(defaultValue, description, validator, required);
    }
    
    template<typename T>
    T GetValue(const std::string& key) const {
        std::lock_guard<std::mutex> lock(mutex_);
        auto it = values_.find(key);
        if (it == values_.end()) {
            throw std::runtime_error("Config key not found: " + key);
        }
        return it->second->Get<T>();
    }
    
    template<typename T>
    bool SetValue(const std::string& key, const T& value) {
        std::lock_guard<std::mutex> lock(mutex_);
        auto it = values_.find(key);
        if (it == values_.end()) {
            return false;
        }
        return it->second->Set(value);
    }
    
    bool HasValue(const std::string& key) const;
    std::vector<std::string> GetKeys() const;
    bool ValidateAll() const;
    
    const std::string& GetName() const { return name_; }
    const std::string& GetDescription() const { return description_; }
    
    // JSON serialization
    nlohmann::json ToJson() const;
    bool FromJson(const nlohmann::json& json);
};

class ConfigManager {
private:
    static std::unique_ptr<ConfigManager> instance_;
    static std::mutex instanceMutex_;
    
    std::unordered_map<std::string, std::unique_ptr<ConfigSection>> sections_;
    std::string configFilePath_;
    mutable std::mutex mutex_;
    bool autoSave_;
    std::vector<std::function<void(const std::string&, const std::string&)>> changeCallbacks_;

public:
    static ConfigManager& GetInstance();
    static void Initialize(const std::string& configFile = "config.json");
    static void Shutdown();
    
    ~ConfigManager() = default;
    
    // Section management
    ConfigSection& CreateSection(const std::string& name, const std::string& description = "");
    ConfigSection& GetSection(const std::string& name);
    const ConfigSection& GetSection(const std::string& name) const;
    bool HasSection(const std::string& name) const;
    std::vector<std::string> GetSectionNames() const;
    
    // Convenience methods for direct value access
    template<typename T>
    T GetValue(const std::string& section, const std::string& key) const {
        return GetSection(section).GetValue<T>(key);
    }
    
    template<typename T>
    bool SetValue(const std::string& section, const std::string& key, const T& value) {
        bool result = GetSection(section).SetValue(key, value);
        if (result && autoSave_) {
            SaveToFile();
        }
        NotifyChange(section, key);
        return result;
    }
    
    // File operations
    bool LoadFromFile(const std::string& filePath = "");
    bool SaveToFile(const std::string& filePath = "") const;
    bool ReloadFromFile();
    
    // Validation
    bool ValidateAll() const;
    std::vector<std::string> GetValidationErrors() const;
    
    // Settings
    void SetAutoSave(bool enabled) { autoSave_ = enabled; }
    bool IsAutoSaveEnabled() const { return autoSave_; }
    
    const std::string& GetConfigFilePath() const { return configFilePath_; }
    void SetConfigFilePath(const std::string& path) { configFilePath_ = path; }
    
    // Change notifications
    void AddChangeCallback(std::function<void(const std::string&, const std::string&)> callback);
    void RemoveAllChangeCallbacks();
    
    // Utility methods
    void PrintConfiguration() const;
    nlohmann::json ToJson() const;
    bool FromJson(const nlohmann::json& json);
    
    // Backup and restore
    bool CreateBackup(const std::string& backupPath = "") const;
    bool RestoreFromBackup(const std::string& backupPath);

private:
    ConfigManager();
    void NotifyChange(const std::string& section, const std::string& key);
    void SetupDefaultConfiguration();
};

// Convenience macros
#define CONFIG_GET(section, key, type) ConfigManager::GetInstance().GetValue<type>(section, key)
#define CONFIG_SET(section, key, value) ConfigManager::GetInstance().SetValue(section, key, value)
#define CONFIG_SECTION(name) ConfigManager::GetInstance().GetSection(name)

// Specialized configuration classes for different components
class ApplicationConfig {
public:
    static void Initialize();
    
    // Application settings
    static std::string GetApplicationName();
    static std::string GetVersion();
    static bool IsDebugMode();
    static void SetDebugMode(bool enabled);
    
    // Logging settings
    static std::string GetLogLevel();
    static void SetLogLevel(const std::string& level);
    static bool IsFileLoggingEnabled();
    static std::string GetLogFilePath();
    static size_t GetMaxLogFileSize();
    
    // UI settings
    static int GetWindowWidth();
    static int GetWindowHeight();
    static void SetWindowSize(int width, int height);
    static std::string GetTheme();
    static void SetTheme(const std::string& theme);
};

class TargetConfig {
public:
    static void Initialize();
    
    // Target process settings
    static std::string GetProcessName();
    static void SetProcessName(const std::string& name);
    static int GetUpdateInterval();
    static void SetUpdateInterval(int interval);
    static int GetMaxRetries();
    static int GetRetryDelay();
};

class StealthConfig {
public:
    static void Initialize();
    
    // Stealth settings
    static bool IsProcessHidingEnabled();
    static bool IsWindowHidingEnabled();
    static bool IsAPIHookingEnabled();
    static bool IsDebuggerDetectionEnabled();
    static bool IsRandomDelayEnabled();
    
    static void SetProcessHiding(bool enabled);
    static void SetWindowHiding(bool enabled);
    static void SetAPIHooking(bool enabled);
    static void SetDebuggerDetection(bool enabled);
    static void SetRandomDelay(bool enabled);
    
    static int GetMinDelayMs();
    static int GetMaxDelayMs();
    static void SetDelayRange(int minMs, int maxMs);
    
    static std::vector<std::string> GetProcessBlacklist();
    static void AddToBlacklist(const std::string& process);
    static void RemoveFromBlacklist(const std::string& process);
};

class SkinConfig {
public:
    static void Initialize();
    
    // Skin settings
    static bool IsAutoApplyEnabled();
    static bool IsSaveOnExitEnabled();
    static bool IsLoadOnStartEnabled();
    static int GetDefaultQuality();
    static float GetDefaultWear();
    static bool IsStatTrakEnabled();
    static bool IsCustomNamesEnabled();
    
    static void SetAutoApply(bool enabled);
    static void SetSaveOnExit(bool enabled);
    static void SetLoadOnStart(bool enabled);
    static void SetDefaultQuality(int quality);
    static void SetDefaultWear(float wear);
    static void SetStatTrakEnabled(bool enabled);
    static void SetCustomNamesEnabled(bool enabled);
};
