#include "skin/SkinEngine.h"
#include "core/MemoryManager.h"
#include "core/PatternScanner.h"
#include <iostream>
#include <fstream>
#include <algorithm>

SkinEngine::SkinEngine() {
    LoadWeaponDefinitions();
    LoadDefaultSkins();
}

bool SkinEngine::Initialize(MemoryManager* memManager, PatternScanner* scanner) {
    if (!memManager || !scanner) {
        Log("[ERROR] MemoryManager ou PatternScanner inválidos");
        return false;
    }
    
    memoryManager = memManager;
    patternScanner = scanner;
    
    if (!InitializeOffsets()) {
        Log("[ERROR] Falha ao inicializar offsets de memória");
        return false;
    }
    
    if (!InitializeWeaponData()) {
        Log("[ERROR] Falha ao inicializar dados de armas");
        return false;
    }
    
    isInitialized = true;
    Log("[INFO] SkinEngine inicializado com sucesso");
    
    return true;
}

void SkinEngine::Shutdown() {
    if (isInitialized) {
        RemoveAllSkins();
        isInitialized = false;
        Log("[INFO] SkinEngine finalizado");
    }
}

bool SkinEngine::ApplySkin(int weaponIndex, const SkinInfo& skin) {
    if (!isInitialized || !IsValidSkin(skin)) {
        return false;
    }
    
    // Encontrar todas as armas do jogador
    auto weapons = GetAllPlayerWeapons();
    bool applied = false;
    
    for (uintptr_t weaponEntity : weapons) {
        int defIndex = ReadWeaponProperty<int>(weaponEntity, offsets.itemDefinitionIndex);
        
        if (defIndex == weaponIndex) {
            if (ApplySkinToWeapon(weaponEntity, skin)) {
                applied = true;
            }
        }
    }
    
    if (applied) {
        activeSkins[weaponIndex] = skin;
        Log("[INFO] Skin aplicada para arma " + std::to_string(weaponIndex));
    }
    
    return applied;
}

bool SkinEngine::RemoveSkin(int weaponIndex) {
    auto it = activeSkins.find(weaponIndex);
    if (it == activeSkins.end()) {
        return false;
    }
    
    // Aplicar skin padrão (paintKit = 0)
    SkinInfo defaultSkin;
    defaultSkin.paintKit = 0;
    defaultSkin.wearFloat = 0.0f;
    defaultSkin.seed = 0;
    defaultSkin.statTrak = -1;
    defaultSkin.quality = 4;
    
    bool removed = ApplySkin(weaponIndex, defaultSkin);
    
    if (removed) {
        activeSkins.erase(it);
        Log("[INFO] Skin removida para arma " + std::to_string(weaponIndex));
    }
    
    return removed;
}

void SkinEngine::ApplyAllSkins() {
    if (!isInitialized) {
        return;
    }
    
    for (const auto& [weaponIndex, skin] : activeSkins) {
        if (skin.enabled) {
            ApplySkin(weaponIndex, skin);
        }
    }
}

void SkinEngine::RemoveAllSkins() {
    std::vector<int> weaponIndices;
    for (const auto& [weaponIndex, skin] : activeSkins) {
        weaponIndices.push_back(weaponIndex);
    }
    
    for (int weaponIndex : weaponIndices) {
        RemoveSkin(weaponIndex);
    }
}

void SkinEngine::SetSkinForWeapon(int weaponIndex, const SkinInfo& skin) {
    activeSkins[weaponIndex] = skin;
}

SkinInfo SkinEngine::GetSkinForWeapon(int weaponIndex) const {
    auto it = activeSkins.find(weaponIndex);
    return it != activeSkins.end() ? it->second : SkinInfo{};
}

bool SkinEngine::HasSkinForWeapon(int weaponIndex) const {
    return activeSkins.find(weaponIndex) != activeSkins.end();
}

const WeaponInfo* SkinEngine::GetWeaponInfo(int definitionIndex) const {
    auto it = weapons.find(definitionIndex);
    return it != weapons.end() ? &it->second : nullptr;
}

std::vector<WeaponInfo> SkinEngine::GetAllWeapons() const {
    std::vector<WeaponInfo> result;
    for (const auto& [index, weapon] : weapons) {
        result.push_back(weapon);
    }
    return result;
}

std::vector<WeaponInfo> SkinEngine::GetWeaponsByCategory(const std::string& category) const {
    std::vector<WeaponInfo> result;
    for (const auto& [index, weapon] : weapons) {
        if (weapon.category == category) {
            result.push_back(weapon);
        }
    }
    return result;
}

void SkinEngine::Update() {
    if (!isInitialized || !autoUpdate) {
        return;
    }
    
    // Verificar se ainda estamos conectados ao processo
    if (!memoryManager->IsProcessValid()) {
        return;
    }
    
    // Aplicar skins ativas automaticamente
    ApplyAllSkins();
}

void SkinEngine::RenderUI() {
    if (!isInitialized) {
        return;
    }
    
    // Esta função será implementada quando criarmos a interface
    // Por enquanto, apenas um placeholder
    RenderStatusPanel();
    RenderWeaponSelector();
}

bool SkinEngine::InitializeOffsets() {
    // Obter offsets dos padrões escaneados
    offsets.localPlayer = patternScanner->GetPatternAddress(CS2Patterns::LOCAL_PLAYER);
    offsets.entityList = patternScanner->GetPatternAddress(CS2Patterns::ENTITY_LIST);
    
    // Offsets específicos (estes valores são exemplos e podem precisar ser atualizados)
    offsets.paintKit = 0x1234;
    offsets.itemDefinitionIndex = 0x1238;
    offsets.itemIDHigh = 0x123C;
    offsets.itemIDLow = 0x1240;
    offsets.accountID = 0x1244;
    offsets.entityQuality = 0x1248;
    offsets.customName = 0x124C;
    offsets.statTrak = 0x1250;
    offsets.wearFloat = 0x1254;
    offsets.seed = 0x1258;
    
    offsets.isValid = (offsets.localPlayer != 0 && offsets.entityList != 0);
    
    return offsets.isValid;
}

bool SkinEngine::InitializeWeaponData() {
    LoadWeaponDefinitions();
    LoadSkinDefinitions();
    return !weapons.empty();
}

void SkinEngine::LoadWeaponDefinitions() {
    // Carregar definições de armas do namespace CS2Weapons
    for (size_t i = 0; i < CS2Weapons::WEAPON_COUNT; ++i) {
        const auto& def = CS2Weapons::WEAPON_DEFINITIONS[i];
        
        WeaponInfo weapon;
        weapon.definitionIndex = def.definitionIndex;
        weapon.name = def.name;
        weapon.displayName = def.displayName;
        weapon.category = def.category;
        weapon.isKnife = def.isKnife;
        weapon.isGlove = def.isGlove;
        
        weapons[def.definitionIndex] = weapon;
    }
}

void SkinEngine::LoadSkinDefinitions() {
    // Esta função carregaria as definições de skins
    // Por enquanto, apenas um placeholder
    Log("[INFO] Definições de skins carregadas");
}

void SkinEngine::LoadDefaultSkins() {
    // Carregar algumas skins padrão para demonstração
    SkinInfo ak47Dragon;
    ak47Dragon.paintKit = 44; // Dragon Lore (exemplo)
    ak47Dragon.wearFloat = 0.1f;
    ak47Dragon.seed = 661;
    ak47Dragon.displayName = "AK-47 | Dragon Lore";
    ak47Dragon.weaponName = "AK-47";
    ak47Dragon.category = "Rifle";
    
    SkinInfo awpDragon;
    awpDragon.paintKit = 344; // Dragon Lore
    awpDragon.wearFloat = 0.05f;
    awpDragon.seed = 420;
    awpDragon.displayName = "AWP | Dragon Lore";
    awpDragon.weaponName = "AWP";
    awpDragon.category = "Sniper";
    
    // Adicionar às armas correspondentes
    if (weapons.find(CS2Weapons::AK47) != weapons.end()) {
        weapons[CS2Weapons::AK47].availableSkins.push_back(ak47Dragon);
    }
    
    if (weapons.find(CS2Weapons::AWP) != weapons.end()) {
        weapons[CS2Weapons::AWP].availableSkins.push_back(awpDragon);
    }
}

bool SkinEngine::ApplySkinToWeapon(uintptr_t weaponEntity, const SkinInfo& skin) {
    if (!IsValidWeaponEntity(weaponEntity)) {
        return false;
    }
    
    bool success = true;
    
    // Aplicar paintKit
    success &= WriteWeaponProperty(weaponEntity, offsets.paintKit, skin.paintKit);
    
    // Aplicar wearFloat
    success &= WriteWeaponProperty(weaponEntity, offsets.wearFloat, skin.wearFloat);
    
    // Aplicar seed
    success &= WriteWeaponProperty(weaponEntity, offsets.seed, skin.seed);
    
    // Aplicar quality
    success &= WriteWeaponProperty(weaponEntity, offsets.entityQuality, skin.quality);
    
    // Aplicar StatTrak se especificado
    if (skin.statTrak >= 0) {
        success &= WriteWeaponProperty(weaponEntity, offsets.statTrak, skin.statTrak);
    }
    
    return success;
}

std::vector<uintptr_t> SkinEngine::GetAllPlayerWeapons() const {
    std::vector<uintptr_t> weapons;
    
    if (!memoryManager || !offsets.isValid) {
        return weapons;
    }
    
    // Esta implementação é simplificada
    // Em um caso real, seria necessário iterar pela lista de entidades
    // e filtrar apenas as armas do jogador local
    
    return weapons;
}

template<typename T>
bool SkinEngine::WriteWeaponProperty(uintptr_t weaponEntity, int offset, const T& value) {
    if (!memoryManager || weaponEntity == 0 || offset == 0) {
        return false;
    }
    
    return memoryManager->WriteMemory(weaponEntity + offset, value);
}

template<typename T>
T SkinEngine::ReadWeaponProperty(uintptr_t weaponEntity, int offset) const {
    if (!memoryManager || weaponEntity == 0 || offset == 0) {
        return T{};
    }
    
    return memoryManager->ReadMemory<T>(weaponEntity + offset);
}

bool SkinEngine::IsValidWeaponEntity(uintptr_t entity) const {
    return entity != 0 && memoryManager && memoryManager->IsValidAddress(entity);
}

bool SkinEngine::IsValidSkin(const SkinInfo& skin) const {
    return skin.paintKit >= 0 && 
           skin.wearFloat >= 0.0f && skin.wearFloat <= 1.0f &&
           skin.quality >= 0 && skin.quality <= 7;
}

void SkinEngine::Log(const std::string& message) const {
    if (logCallback) {
        logCallback(message);
    } else {
        std::cout << message << std::endl;
    }
}

void SkinEngine::RenderStatusPanel() {
    // Placeholder para painel de status
}

void SkinEngine::RenderWeaponSelector() {
    // Placeholder para seletor de armas
}

std::string SkinEngine::GetStatusString() const {
    if (!isInitialized) {
        return "Não inicializado";
    }
    
    if (!memoryManager || !memoryManager->IsProcessValid()) {
        return "Desconectado";
    }
    
    return "Ativo (" + std::to_string(activeSkins.size()) + " skins)";
}

std::vector<int> SkinEngine::GetActiveSkinWeapons() const {
    std::vector<int> weapons;
    for (const auto& [weaponIndex, skin] : activeSkins) {
        weapons.push_back(weaponIndex);
    }
    return weapons;
}

// Implementação das definições de armas
namespace CS2Weapons {
    const WeaponDefinition WEAPON_DEFINITIONS[] = {
        {AK47, "ak47", "AK-47", "Rifle", false, false},
        {M4A4, "m4a4", "M4A4", "Rifle", false, false},
        {M4A1_S, "m4a1_s", "M4A1-S", "Rifle", false, false},
        {AWP, "awp", "AWP", "Sniper", false, false},
        {GLOCK, "glock", "Glock-18", "Pistol", false, false},
        {USP_S, "usp_s", "USP-S", "Pistol", false, false},
        {DEAGLE, "deagle", "Desert Eagle", "Pistol", false, false},
        {KNIFE_BAYONET, "knife_bayonet", "Bayonet", "Knife", true, false},
        {KNIFE_KARAMBIT, "knife_karambit", "Karambit", "Knife", true, false},
        {KNIFE_M9_BAYONET, "knife_m9_bayonet", "M9 Bayonet", "Knife", true, false},
        {GLOVE_STUDDED_BLOODHOUND, "glove_studded_bloodhound", "Bloodhound Gloves", "Glove", false, true},
        {GLOVE_T_SIDE, "glove_t_side", "T-Side Gloves", "Glove", false, true},
        {GLOVE_CT_SIDE, "glove_ct_side", "CT-Side Gloves", "Glove", false, true}
    };
    
    const size_t WEAPON_COUNT = sizeof(WEAPON_DEFINITIONS) / sizeof(WeaponDefinition);
}
