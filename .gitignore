# Build directories
build/
bin/
obj/
out/

# Visual Studio
.vs/
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates

# Visual Studio Code
.vscode/

# Compiled Object files
*.o
*.obj
*.elf

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# CMake
CMakeCache.txt
CMakeFiles/
CMakeScripts/
Testing/
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps

# Logs
logs/
*.log

# Temporary files
*.tmp
*.temp
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
*.swp
*.swo
*~

# Package managers
vcpkg_installed/
packages/

# Backup files
*.bak
*.backup

# Configuration files with sensitive data
config_local.json
secrets.json

# Test files
test_results/
coverage/

# Documentation build
docs/_build/
docs/html/
docs/latex/

# Profiling data
*.prof
*.gprof

# Memory dumps
*.dmp
*.mdmp

# Crash dumps
crash_*.txt

# Local environment
.env
.env.local

# JetBrains IDEs
.idea/

# Conan
conandata.yml
conanfile.txt
conaninfo.txt

# vcpkg
vcpkg.json
.vcpkg-root

# Windows specific
*.lnk
desktop.ini

# macOS specific
.AppleDouble
.LSOverride

# Linux specific
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Ignore any local configuration overrides
*_local.*
local_*
