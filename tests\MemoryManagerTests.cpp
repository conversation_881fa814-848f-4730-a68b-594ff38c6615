#include "TestFramework.h"
#include "../include/core/MemoryManager.h"
#include "../include/utils/Logger.h"
#include <windows.h>

class MemoryManagerTestSuite {
private:
    std::unique_ptr<MemoryManager> memoryManager;
    DWORD currentProcessId;

public:
    void Setup() {
        memoryManager = std::make_unique<MemoryManager>();
        currentProcessId = GetCurrentProcessId();
    }
    
    void Teardown() {
        if (memoryManager) {
            memoryManager->DetachFromProcess();
            memoryManager.reset();
        }
    }
    
    void TestConstructorDestructor() {
        // Test that constructor doesn't throw
        ASSERT_NO_THROW([]() {
            MemoryManager manager;
        });
        
        // Test that destructor properly cleans up
        {
            MemoryManager manager;
            // Should not crash when going out of scope
        }
    }
    
    void TestAttachToCurrentProcess() {
        // Get current process name
        char processName[MAX_PATH];
        GetModuleFileNameA(nullptr, processName, MAX_PATH);
        std::string currentProcessName = std::filesystem::path(processName).filename().string();
        
        // Test attaching to current process
        bool result = memoryManager->AttachToProcess(currentProcessName);
        ASSERT_TRUE(result);
        
        // Verify process information
        ASSERT_EQUAL(currentProcessId, memoryManager->GetProcessId());
        ASSERT_NOT_NULL(memoryManager->GetProcessHandle());
        ASSERT_TRUE(memoryManager->IsProcessValid());
        ASSERT_GREATER(memoryManager->GetBaseAddress(), static_cast<uintptr_t>(0));
        ASSERT_GREATER(memoryManager->GetModuleSize(), static_cast<size_t>(0));
    }
    
    void TestAttachToNonExistentProcess() {
        // Test attaching to non-existent process
        bool result = memoryManager->AttachToProcess("nonexistent_process.exe");
        ASSERT_FALSE(result);
        
        // Verify process is not valid
        ASSERT_FALSE(memoryManager->IsProcessValid());
        ASSERT_EQUAL(static_cast<DWORD>(0), memoryManager->GetProcessId());
    }
    
    void TestDetachFromProcess() {
        // First attach to current process
        char processName[MAX_PATH];
        GetModuleFileNameA(nullptr, processName, MAX_PATH);
        std::string currentProcessName = std::filesystem::path(processName).filename().string();
        
        memoryManager->AttachToProcess(currentProcessName);
        ASSERT_TRUE(memoryManager->IsProcessValid());
        
        // Then detach
        memoryManager->DetachFromProcess();
        ASSERT_FALSE(memoryManager->IsProcessValid());
        ASSERT_EQUAL(static_cast<DWORD>(0), memoryManager->GetProcessId());
    }
    
    void TestReadWriteMemory() {
        // Attach to current process first
        char processName[MAX_PATH];
        GetModuleFileNameA(nullptr, processName, MAX_PATH);
        std::string currentProcessName = std::filesystem::path(processName).filename().string();
        
        bool attached = memoryManager->AttachToProcess(currentProcessName);
        ASSERT_TRUE(attached);
        
        // Test reading/writing to a local variable
        int testValue = 12345;
        uintptr_t testAddress = reinterpret_cast<uintptr_t>(&testValue);
        
        // Test reading
        int readValue = 0;
        bool readResult = memoryManager->ReadMemory(testAddress, readValue);
        ASSERT_TRUE(readResult);
        ASSERT_EQUAL(testValue, readValue);
        
        // Test writing
        int newValue = 54321;
        bool writeResult = memoryManager->WriteMemory(testAddress, newValue);
        ASSERT_TRUE(writeResult);
        ASSERT_EQUAL(newValue, testValue); // testValue should be changed
        
        // Test template version
        int templateRead = memoryManager->ReadMemory<int>(testAddress);
        ASSERT_EQUAL(newValue, templateRead);
    }
    
    void TestReadWriteBuffer() {
        // Attach to current process first
        char processName[MAX_PATH];
        GetModuleFileNameA(nullptr, processName, MAX_PATH);
        std::string currentProcessName = std::filesystem::path(processName).filename().string();
        
        bool attached = memoryManager->AttachToProcess(currentProcessName);
        ASSERT_TRUE(attached);
        
        // Test buffer operations
        char testBuffer[256];
        strcpy_s(testBuffer, "Hello, World!");
        uintptr_t bufferAddress = reinterpret_cast<uintptr_t>(testBuffer);
        
        // Read buffer
        char readBuffer[256] = {0};
        bool readResult = memoryManager->ReadMemory(bufferAddress, readBuffer, strlen(testBuffer) + 1);
        ASSERT_TRUE(readResult);
        ASSERT_EQUAL(std::string(testBuffer), std::string(readBuffer));
        
        // Write buffer
        const char* newData = "Modified data!";
        bool writeResult = memoryManager->WriteMemory(bufferAddress, newData, strlen(newData) + 1);
        ASSERT_TRUE(writeResult);
        ASSERT_EQUAL(std::string(newData), std::string(testBuffer));
    }
    
    void TestMemoryProtection() {
        // Attach to current process first
        char processName[MAX_PATH];
        GetModuleFileNameA(nullptr, processName, MAX_PATH);
        std::string currentProcessName = std::filesystem::path(processName).filename().string();
        
        bool attached = memoryManager->AttachToProcess(currentProcessName);
        ASSERT_TRUE(attached);
        
        // Allocate some memory to test protection
        void* testMemory = VirtualAlloc(nullptr, 4096, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        ASSERT_NOT_NULL(testMemory);
        
        uintptr_t testAddress = reinterpret_cast<uintptr_t>(testMemory);
        
        // Test changing protection
        DWORD oldProtection;
        bool protectResult = memoryManager->ProtectMemory(testAddress, 4096, PAGE_READONLY, oldProtection);
        ASSERT_TRUE(protectResult);
        ASSERT_EQUAL(static_cast<DWORD>(PAGE_READWRITE), oldProtection);
        
        // Test restoring protection
        bool restoreResult = memoryManager->RestoreMemoryProtection(testAddress, 4096, oldProtection);
        ASSERT_TRUE(restoreResult);
        
        // Cleanup
        VirtualFree(testMemory, 0, MEM_RELEASE);
    }
    
    void TestIsValidAddress() {
        // Attach to current process first
        char processName[MAX_PATH];
        GetModuleFileNameA(nullptr, processName, MAX_PATH);
        std::string currentProcessName = std::filesystem::path(processName).filename().string();
        
        bool attached = memoryManager->AttachToProcess(currentProcessName);
        ASSERT_TRUE(attached);
        
        // Test valid address (stack variable)
        int stackVar = 42;
        uintptr_t validAddress = reinterpret_cast<uintptr_t>(&stackVar);
        ASSERT_TRUE(memoryManager->IsValidAddress(validAddress));
        
        // Test invalid address
        uintptr_t invalidAddress = 0x12345678; // Likely invalid
        // Note: This might be valid on some systems, so we can't assert false definitively
        
        // Test null address
        ASSERT_FALSE(memoryManager->IsValidAddress(0));
    }
    
    void TestPatternSearching() {
        // Attach to current process first
        char processName[MAX_PATH];
        GetModuleFileNameA(nullptr, processName, MAX_PATH);
        std::string currentProcessName = std::filesystem::path(processName).filename().string();
        
        bool attached = memoryManager->AttachToProcess(currentProcessName);
        ASSERT_TRUE(attached);
        
        // Create a known pattern in memory
        uint8_t testPattern[] = {0xDE, 0xAD, 0xBE, 0xEF, 0xCA, 0xFE, 0xBA, 0xBE};
        std::string pattern(reinterpret_cast<char*>(testPattern), sizeof(testPattern));
        std::string mask = "xxxxxxxx";
        
        // This test is limited because we can't easily create a searchable pattern
        // in the current process's memory space that we can guarantee to find
        
        // Test that the function doesn't crash
        ASSERT_NO_THROW([&]() {
            auto results = memoryManager->FindPattern(pattern, mask);
            // Results might be empty, which is fine for this test
        });
    }
    
    void TestPerformance() {
        PERF_TEST("MemoryManager Attach/Detach", 1000); // 1 second max
        
        char processName[MAX_PATH];
        GetModuleFileNameA(nullptr, processName, MAX_PATH);
        std::string currentProcessName = std::filesystem::path(processName).filename().string();
        
        // Test attach performance
        bool attached = memoryManager->AttachToProcess(currentProcessName);
        ASSERT_TRUE(attached);
        
        // Test read performance
        int testValue = 12345;
        uintptr_t testAddress = reinterpret_cast<uintptr_t>(&testValue);
        
        {
            PERF_TEST("1000 Memory Reads", 100); // 100ms max for 1000 reads
            for (int i = 0; i < 1000; ++i) {
                int value;
                memoryManager->ReadMemory(testAddress, value);
            }
        }
        
        memoryManager->DetachFromProcess();
    }
};

// Function to create and run the test suite
std::unique_ptr<TestSuite> CreateMemoryManagerTests() {
    auto suite = std::make_unique<TestSuite>("MemoryManager Tests");
    auto testInstance = std::make_shared<MemoryManagerTestSuite>();
    
    // Setup and teardown
    suite->SetSetup([testInstance]() { testInstance->Setup(); });
    suite->SetTeardown([testInstance]() { testInstance->Teardown(); });
    
    // Add tests
    suite->AddTest("Constructor/Destructor", [testInstance]() { 
        testInstance->TestConstructorDestructor(); 
    }, "Basic");
    
    suite->AddTest("Attach to Current Process", [testInstance]() { 
        testInstance->TestAttachToCurrentProcess(); 
    }, "Process Management");
    
    suite->AddTest("Attach to Non-existent Process", [testInstance]() { 
        testInstance->TestAttachToNonExistentProcess(); 
    }, "Process Management");
    
    suite->AddTest("Detach from Process", [testInstance]() { 
        testInstance->TestDetachFromProcess(); 
    }, "Process Management");
    
    suite->AddTest("Read/Write Memory", [testInstance]() { 
        testInstance->TestReadWriteMemory(); 
    }, "Memory Operations");
    
    suite->AddTest("Read/Write Buffer", [testInstance]() { 
        testInstance->TestReadWriteBuffer(); 
    }, "Memory Operations");
    
    suite->AddTest("Memory Protection", [testInstance]() { 
        testInstance->TestMemoryProtection(); 
    }, "Memory Operations");
    
    suite->AddTest("Address Validation", [testInstance]() { 
        testInstance->TestIsValidAddress(); 
    }, "Validation");
    
    suite->AddTest("Pattern Searching", [testInstance]() { 
        testInstance->TestPatternSearching(); 
    }, "Pattern Scanning");
    
    suite->AddTest("Performance", [testInstance]() { 
        testInstance->TestPerformance(); 
    }, "Performance");
    
    return suite;
}
