#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <memory>

class MemoryManager;

struct PatternInfo {
    std::string name;
    std::string pattern;
    std::string mask;
    uintptr_t address = 0;
    bool found = false;
    int offset = 0; // Offset adicional após encontrar o padrão
};

class PatternScanner {
private:
    std::unordered_map<std::string, PatternInfo> patterns;
    MemoryManager* memoryManager = nullptr;

public:
    PatternScanner();
    ~PatternScanner() = default;

    // Gerenciamento de padrões
    void AddPattern(const std::string& name, const std::string& pattern, 
                   const std::string& mask, int offset = 0);
    bool RemovePattern(const std::string& name);
    void ClearPatterns();

    // Scanning
    bool ScanPatterns(MemoryManager* memManager);
    bool ScanPattern(const std::string& name);
    bool ScanAllPatterns();

    // Acesso aos resultados
    uintptr_t GetPatternAddress(const std::string& name) const;
    bool IsPatternFound(const std::string& name) const;
    const PatternInfo* GetPatternInfo(const std::string& name) const;
    
    // Utilitários
    std::vector<std::string> GetFoundPatterns() const;
    std::vector<std::string> GetMissingPatterns() const;
    size_t GetFoundCount() const;
    size_t GetTotalCount() const;

    // Padrões específicos do CS2
    void InitializeCS2Patterns();

private:
    // Conversão de string hex para bytes
    std::vector<uint8_t> HexStringToBytes(const std::string& hex) const;
    std::string CreateMaskFromPattern(const std::string& pattern) const;
    
    // Validação
    bool ValidatePattern(const std::string& pattern, const std::string& mask) const;
};

// Namespace com padrões conhecidos do CS2
namespace CS2Patterns {
    // Padrões para localizar estruturas importantes
    constexpr const char* CLIENT_STATE = "ClientState";
    constexpr const char* LOCAL_PLAYER = "LocalPlayer";
    constexpr const char* ENTITY_LIST = "EntityList";
    constexpr const char* WEAPON_DATA = "WeaponData";
    constexpr const char* SKIN_DATA = "SkinData";
    constexpr const char* PAINT_KIT = "PaintKit";
    constexpr const char* ITEM_DEFINITION_INDEX = "ItemDefinitionIndex";
    constexpr const char* ITEM_ID_HIGH = "ItemIDHigh";
    constexpr const char* ITEM_ID_LOW = "ItemIDLow";
    constexpr const char* ACCOUNT_ID = "AccountID";
    constexpr const char* ENTITY_QUALITY = "EntityQuality";
    constexpr const char* CUSTOM_NAME = "CustomName";
    constexpr const char* STATTRAK = "StatTrak";
    constexpr const char* WEAR_FLOAT = "WearFloat";
    constexpr const char* SEED = "Seed";
    
    // Estrutura para armazenar informações de padrões
    struct PatternData {
        const char* name;
        const char* pattern;
        const char* mask;
        int offset;
    };
    
    // Array com todos os padrões conhecidos
    extern const PatternData KNOWN_PATTERNS[];
    extern const size_t PATTERN_COUNT;
}
