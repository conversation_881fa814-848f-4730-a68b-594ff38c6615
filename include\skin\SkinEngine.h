#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <functional>

class MemoryManager;
class PatternScanner;

// Estrutura para representar uma skin
struct SkinInfo {
    int paintKit = 0;
    float wearFloat = 0.0f;
    int seed = 0;
    int statTrak = -1;
    std::string customName;
    int quality = 4; // 4 = Unique
    bool enabled = false;
    
    // Metadados
    std::string displayName;
    std::string weaponName;
    std::string category;
    bool isKnife = false;
    bool isGlove = false;
};

// Estrutura para representar uma arma
struct WeaponInfo {
    int definitionIndex = 0;
    std::string name;
    std::string displayName;
    std::string category;
    bool isKnife = false;
    bool isGlove = false;
    std::vector<SkinInfo> availableSkins;
};

// Estrutura para offsets de memória
struct MemoryOffsets {
    uintptr_t localPlayer = 0;
    uintptr_t entityList = 0;
    
    // Offsets de item
    int paintKit = 0;
    int itemDefinitionIndex = 0;
    int itemIDHigh = 0;
    int itemIDLow = 0;
    int accountID = 0;
    int entityQuality = 0;
    int customName = 0;
    int statTrak = 0;
    int wearFloat = 0;
    int seed = 0;
    
    bool isValid = false;
};

class SkinEngine {
private:
    MemoryManager* memoryManager = nullptr;
    PatternScanner* patternScanner = nullptr;
    MemoryOffsets offsets;
    
    std::unordered_map<int, WeaponInfo> weapons;
    std::unordered_map<int, SkinInfo> activeSkins;
    
    bool isInitialized = false;
    bool autoUpdate = true;
    
    // Callbacks
    std::function<void(const std::string&)> logCallback;

public:
    SkinEngine();
    ~SkinEngine() = default;

    // Inicialização
    bool Initialize(MemoryManager* memManager, PatternScanner* scanner);
    void Shutdown();
    bool IsInitialized() const { return isInitialized; }

    // Gerenciamento de skins
    bool ApplySkin(int weaponIndex, const SkinInfo& skin);
    bool RemoveSkin(int weaponIndex);
    void ApplyAllSkins();
    void RemoveAllSkins();
    
    // Configuração de skins
    void SetSkinForWeapon(int weaponIndex, const SkinInfo& skin);
    SkinInfo GetSkinForWeapon(int weaponIndex) const;
    bool HasSkinForWeapon(int weaponIndex) const;
    
    // Informações de armas
    const WeaponInfo* GetWeaponInfo(int definitionIndex) const;
    std::vector<WeaponInfo> GetAllWeapons() const;
    std::vector<WeaponInfo> GetWeaponsByCategory(const std::string& category) const;
    
    // Atualização
    void Update();
    void SetAutoUpdate(bool enabled) { autoUpdate = enabled; }
    bool IsAutoUpdateEnabled() const { return autoUpdate; }
    
    // Interface de usuário
    void RenderUI();
    
    // Configuração
    bool LoadSkinsFromFile(const std::string& filename);
    bool SaveSkinsToFile(const std::string& filename) const;
    void LoadDefaultSkins();
    
    // Utilitários
    void SetLogCallback(std::function<void(const std::string&)> callback) { logCallback = callback; }
    std::string GetStatusString() const;
    
    // Informações do sistema
    size_t GetActiveSkinCount() const { return activeSkins.size(); }
    std::vector<int> GetActiveSkinWeapons() const;

private:
    // Inicialização interna
    bool InitializeOffsets();
    bool InitializeWeaponData();
    void LoadWeaponDefinitions();
    void LoadSkinDefinitions();
    
    // Aplicação de skins
    bool ApplySkinToWeapon(uintptr_t weaponEntity, const SkinInfo& skin);
    uintptr_t GetLocalPlayerWeapon(int slot) const;
    uintptr_t GetWeaponEntity(int entityIndex) const;
    std::vector<uintptr_t> GetAllPlayerWeapons() const;
    
    // Leitura/escrita de memória
    template<typename T>
    bool WriteWeaponProperty(uintptr_t weaponEntity, int offset, const T& value);
    
    template<typename T>
    T ReadWeaponProperty(uintptr_t weaponEntity, int offset) const;
    
    // Validação
    bool IsValidWeaponEntity(uintptr_t entity) const;
    bool IsValidSkin(const SkinInfo& skin) const;
    
    // Logging
    void Log(const std::string& message) const;
    
    // UI helpers
    void RenderWeaponSelector();
    void RenderSkinSelector(int weaponIndex);
    void RenderSkinProperties(SkinInfo& skin);
    void RenderStatusPanel();
};

// Namespace com definições de armas e skins
namespace CS2Weapons {
    // IDs de definição de armas
    constexpr int AK47 = 7;
    constexpr int M4A4 = 16;
    constexpr int M4A1_S = 60;
    constexpr int AWP = 9;
    constexpr int GLOCK = 2;
    constexpr int USP_S = 61;
    constexpr int DEAGLE = 1;
    
    // Facas
    constexpr int KNIFE_BAYONET = 500;
    constexpr int KNIFE_KARAMBIT = 507;
    constexpr int KNIFE_M9_BAYONET = 508;
    
    // Luvas
    constexpr int GLOVE_STUDDED_BLOODHOUND = 5027;
    constexpr int GLOVE_T_SIDE = 5028;
    constexpr int GLOVE_CT_SIDE = 5029;
    
    // Estrutura para definições de armas
    struct WeaponDefinition {
        int definitionIndex;
        const char* name;
        const char* displayName;
        const char* category;
        bool isKnife;
        bool isGlove;
    };
    
    // Array com todas as definições de armas
    extern const WeaponDefinition WEAPON_DEFINITIONS[];
    extern const size_t WEAPON_COUNT;
}

// Namespace com definições de skins
namespace CS2Skins {
    // Estrutura para definições de skins
    struct SkinDefinition {
        int paintKit;
        const char* name;
        const char* displayName;
        int weaponIndex;
        bool isRare;
    };
    
    // Arrays com definições de skins por categoria
    extern const SkinDefinition RIFLE_SKINS[];
    extern const SkinDefinition PISTOL_SKINS[];
    extern const SkinDefinition KNIFE_SKINS[];
    extern const SkinDefinition GLOVE_SKINS[];
    
    extern const size_t RIFLE_SKIN_COUNT;
    extern const size_t PISTOL_SKIN_COUNT;
    extern const size_t KNIFE_SKIN_COUNT;
    extern const size_t GLOVE_SKIN_COUNT;
}
