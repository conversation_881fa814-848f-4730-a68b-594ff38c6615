#pragma once

#include <string>
#include <vector>
#include <functional>
#include <chrono>
#include <memory>
#include <iostream>
#include <sstream>
#include <exception>

// Test result structure
struct TestResult {
    std::string testName;
    bool passed;
    std::string errorMessage;
    std::chrono::milliseconds duration;
    std::string category;
    
    TestResult(const std::string& name, bool success, const std::string& error = "", 
              std::chrono::milliseconds dur = std::chrono::milliseconds(0),
              const std::string& cat = "General")
        : testName(name), passed(success), errorMessage(error), duration(dur), category(cat) {}
};

// Test suite class
class TestSuite {
private:
    std::string suiteName_;
    std::vector<std::function<void()>> tests_;
    std::vector<TestResult> results_;
    std::function<void()> setupFunction_;
    std::function<void()> teardownFunction_;

public:
    TestSuite(const std::string& name) : suiteName_(name) {}
    
    // Add test functions
    void AddTest(const std::string& testName, std::function<void()> testFunc, 
                const std::string& category = "General");
    
    // Setup and teardown
    void SetSetup(std::function<void()> setup) { setupFunction_ = setup; }
    void SetTeardown(std::function<void()> teardown) { teardownFunction_ = teardown; }
    
    // Run tests
    void RunAll();
    void RunTest(const std::string& testName);
    void RunCategory(const std::string& category);
    
    // Results
    const std::vector<TestResult>& GetResults() const { return results_; }
    int GetPassedCount() const;
    int GetFailedCount() const;
    int GetTotalCount() const { return static_cast<int>(results_.size()); }
    
    // Reporting
    void PrintResults() const;
    void PrintSummary() const;
    std::string GenerateReport() const;
    bool SaveReport(const std::string& filename) const;
    
    const std::string& GetName() const { return suiteName_; }

private:
    void RunSingleTest(const std::string& testName, std::function<void()> testFunc, 
                      const std::string& category);
};

// Test runner for multiple suites
class TestRunner {
private:
    std::vector<std::unique_ptr<TestSuite>> suites_;
    bool stopOnFirstFailure_;
    bool verboseOutput_;

public:
    TestRunner(bool stopOnFailure = false, bool verbose = true)
        : stopOnFirstFailure_(stopOnFailure), verboseOutput_(verbose) {}
    
    void AddSuite(std::unique_ptr<TestSuite> suite);
    void RunAllSuites();
    void RunSuite(const std::string& suiteName);
    
    void SetStopOnFirstFailure(bool stop) { stopOnFirstFailure_ = stop; }
    void SetVerboseOutput(bool verbose) { verboseOutput_ = verbose; }
    
    // Results
    void PrintOverallSummary() const;
    std::string GenerateOverallReport() const;
    bool SaveOverallReport(const std::string& filename) const;
    
    int GetTotalTests() const;
    int GetTotalPassed() const;
    int GetTotalFailed() const;
};

// Assertion macros and functions
class TestAssert {
public:
    static void IsTrue(bool condition, const std::string& message = "");
    static void IsFalse(bool condition, const std::string& message = "");
    static void IsNull(void* ptr, const std::string& message = "");
    static void IsNotNull(void* ptr, const std::string& message = "");
    
    template<typename T>
    static void AreEqual(const T& expected, const T& actual, const std::string& message = "") {
        if (!(expected == actual)) {
            std::ostringstream oss;
            oss << "Expected: " << expected << ", Actual: " << actual;
            if (!message.empty()) {
                oss << " - " << message;
            }
            throw std::runtime_error(oss.str());
        }
    }
    
    template<typename T>
    static void AreNotEqual(const T& expected, const T& actual, const std::string& message = "") {
        if (expected == actual) {
            std::ostringstream oss;
            oss << "Values should not be equal: " << expected;
            if (!message.empty()) {
                oss << " - " << message;
            }
            throw std::runtime_error(oss.str());
        }
    }
    
    template<typename T>
    static void IsGreater(const T& value, const T& threshold, const std::string& message = "") {
        if (!(value > threshold)) {
            std::ostringstream oss;
            oss << "Value " << value << " should be greater than " << threshold;
            if (!message.empty()) {
                oss << " - " << message;
            }
            throw std::runtime_error(oss.str());
        }
    }
    
    template<typename T>
    static void IsLess(const T& value, const T& threshold, const std::string& message = "") {
        if (!(value < threshold)) {
            std::ostringstream oss;
            oss << "Value " << value << " should be less than " << threshold;
            if (!message.empty()) {
                oss << " - " << message;
            }
            throw std::runtime_error(oss.str());
        }
    }
    
    template<typename T>
    static void IsInRange(const T& value, const T& min, const T& max, const std::string& message = "") {
        if (value < min || value > max) {
            std::ostringstream oss;
            oss << "Value " << value << " should be between " << min << " and " << max;
            if (!message.empty()) {
                oss << " - " << message;
            }
            throw std::runtime_error(oss.str());
        }
    }
    
    static void Throws(std::function<void()> func, const std::string& message = "");
    
    template<typename ExceptionType>
    static void ThrowsType(std::function<void()> func, const std::string& message = "") {
        bool threw = false;
        bool correctType = false;
        
        try {
            func();
        } catch (const ExceptionType&) {
            threw = true;
            correctType = true;
        } catch (...) {
            threw = true;
        }
        
        if (!threw) {
            throw std::runtime_error("Expected exception was not thrown" + 
                                   (message.empty() ? "" : " - " + message));
        }
        
        if (!correctType) {
            throw std::runtime_error("Wrong exception type was thrown" + 
                                   (message.empty() ? "" : " - " + message));
        }
    }
    
    static void DoesNotThrow(std::function<void()> func, const std::string& message = "");
};

// Convenience macros
#define ASSERT_TRUE(condition) TestAssert::IsTrue(condition, #condition)
#define ASSERT_FALSE(condition) TestAssert::IsFalse(condition, #condition)
#define ASSERT_NULL(ptr) TestAssert::IsNull(ptr, #ptr)
#define ASSERT_NOT_NULL(ptr) TestAssert::IsNotNull(ptr, #ptr)
#define ASSERT_EQUAL(expected, actual) TestAssert::AreEqual(expected, actual, #expected " == " #actual)
#define ASSERT_NOT_EQUAL(expected, actual) TestAssert::AreNotEqual(expected, actual, #expected " != " #actual)
#define ASSERT_GREATER(value, threshold) TestAssert::IsGreater(value, threshold, #value " > " #threshold)
#define ASSERT_LESS(value, threshold) TestAssert::IsLess(value, threshold, #value " < " #threshold)
#define ASSERT_IN_RANGE(value, min, max) TestAssert::IsInRange(value, min, max, #value " in [" #min ", " #max "]")
#define ASSERT_THROWS(func) TestAssert::Throws(func, #func)
#define ASSERT_THROWS_TYPE(type, func) TestAssert::ThrowsType<type>(func, #func)
#define ASSERT_NO_THROW(func) TestAssert::DoesNotThrow(func, #func)

// Performance testing utilities
class PerformanceTest {
private:
    std::string testName_;
    std::chrono::high_resolution_clock::time_point startTime_;
    std::chrono::milliseconds maxDuration_;

public:
    PerformanceTest(const std::string& name, std::chrono::milliseconds maxDuration)
        : testName_(name), maxDuration_(maxDuration) {
        startTime_ = std::chrono::high_resolution_clock::now();
    }
    
    ~PerformanceTest() {
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime_);
        
        if (duration > maxDuration_) {
            std::ostringstream oss;
            oss << "Performance test '" << testName_ << "' took " << duration.count() 
                << "ms, expected max " << maxDuration_.count() << "ms";
            throw std::runtime_error(oss.str());
        }
    }
    
    std::chrono::milliseconds GetElapsed() const {
        auto now = std::chrono::high_resolution_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime_);
    }
};

#define PERF_TEST(name, maxMs) PerformanceTest _perf_test(name, std::chrono::milliseconds(maxMs))

// Mock and stub utilities
template<typename T>
class MockObject {
private:
    std::unordered_map<std::string, std::function<T()>> methodMocks_;
    std::unordered_map<std::string, int> callCounts_;

public:
    void SetMock(const std::string& methodName, std::function<T()> mockFunc) {
        methodMocks_[methodName] = mockFunc;
    }
    
    T CallMock(const std::string& methodName) {
        callCounts_[methodName]++;
        auto it = methodMocks_.find(methodName);
        if (it != methodMocks_.end()) {
            return it->second();
        }
        throw std::runtime_error("No mock set for method: " + methodName);
    }
    
    int GetCallCount(const std::string& methodName) const {
        auto it = callCounts_.find(methodName);
        return it != callCounts_.end() ? it->second : 0;
    }
    
    void ResetCalls() {
        callCounts_.clear();
    }
    
    void VerifyCallCount(const std::string& methodName, int expectedCount) const {
        int actualCount = GetCallCount(methodName);
        if (actualCount != expectedCount) {
            std::ostringstream oss;
            oss << "Method '" << methodName << "' was called " << actualCount 
                << " times, expected " << expectedCount;
            throw std::runtime_error(oss.str());
        }
    }
};

// Test data generators
class TestDataGenerator {
public:
    static std::vector<int> GenerateIntRange(int start, int end, int step = 1);
    static std::vector<float> GenerateFloatRange(float start, float end, float step);
    static std::vector<std::string> GenerateRandomStrings(int count, int minLength, int maxLength);
    static std::vector<uint8_t> GenerateRandomBytes(size_t count);
    static std::string GenerateRandomString(int length);
    static int GenerateRandomInt(int min, int max);
    static float GenerateRandomFloat(float min, float max);
};
