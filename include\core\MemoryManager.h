#pragma once

#include <windows.h>
#include <tlhelp32.h>
#include <psapi.h>
#include <string>
#include <vector>
#include <memory>

class MemoryManager {
private:
    HANDLE processHandle = INVALID_HANDLE_VALUE;
    DWORD processId = 0;
    uintptr_t baseAddress = 0;
    size_t moduleSize = 0;
    std::string processName;

public:
    MemoryManager();
    ~MemoryManager();

    // Gerenciamento de processo
    bool AttachToProcess(const std::string& processName);
    bool IsProcessValid() const;
    void DetachFromProcess();

    // Informações do processo
    HANDLE GetProcessHandle() const { return processHandle; }
    DWORD GetProcessId() const { return processId; }
    uintptr_t GetBaseAddress() const { return baseAddress; }
    size_t GetModuleSize() const { return moduleSize; }

    // Leitura de memória
    template<typename T>
    bool ReadMemory(uintptr_t address, T& value) const {
        SIZE_T bytesRead;
        return ReadProcessMemory(processHandle, reinterpret_cast<LPCVOID>(address), 
                               &value, sizeof(T), &bytesRead) && bytesRead == sizeof(T);
    }

    bool ReadMemory(uintptr_t address, void* buffer, size_t size) const;
    
    template<typename T>
    T ReadMemory(uintptr_t address) const {
        T value{};
        ReadMemory(address, value);
        return value;
    }

    // Escrita de memória
    template<typename T>
    bool WriteMemory(uintptr_t address, const T& value) const {
        SIZE_T bytesWritten;
        return WriteProcessMemory(processHandle, reinterpret_cast<LPVOID>(address), 
                                &value, sizeof(T), &bytesWritten) && bytesWritten == sizeof(T);
    }

    bool WriteMemory(uintptr_t address, const void* buffer, size_t size) const;

    // Proteção de memória
    bool ProtectMemory(uintptr_t address, size_t size, DWORD newProtection, DWORD& oldProtection) const;
    bool RestoreMemoryProtection(uintptr_t address, size_t size, DWORD protection) const;

    // Busca de padrões
    std::vector<uintptr_t> FindPattern(const std::string& pattern, const std::string& mask) const;
    uintptr_t FindPatternFirst(const std::string& pattern, const std::string& mask) const;

    // Utilitários
    bool IsValidAddress(uintptr_t address) const;
    std::vector<MEMORY_BASIC_INFORMATION> GetMemoryRegions() const;

private:
    DWORD FindProcessId(const std::string& processName) const;
    uintptr_t GetModuleBaseAddress(DWORD processId, const std::string& moduleName) const;
    size_t GetModuleSize(DWORD processId, const std::string& moduleName) const;
    bool SetDebugPrivileges();
};

// Classe auxiliar para gerenciar proteção de memória automaticamente
class MemoryProtectionGuard {
private:
    const MemoryManager* memManager;
    uintptr_t address;
    size_t size;
    DWORD originalProtection;
    bool isActive;

public:
    MemoryProtectionGuard(const MemoryManager* manager, uintptr_t addr, size_t sz, DWORD newProtection);
    ~MemoryProtectionGuard();
    
    bool IsActive() const { return isActive; }
    
    // Não permitir cópia
    MemoryProtectionGuard(const MemoryProtectionGuard&) = delete;
    MemoryProtectionGuard& operator=(const MemoryProtectionGuard&) = delete;
    
    // Permitir movimento
    MemoryProtectionGuard(MemoryProtectionGuard&& other) noexcept;
    MemoryProtectionGuard& operator=(MemoryProtectionGuard&& other) noexcept;
};
